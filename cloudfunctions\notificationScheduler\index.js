// notificationScheduler/index.js
// 通知调度云函数，处理定时任务

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 数据库集合
const NOTIFICATIONS = 'notifications';
const USERS = 'users';
const COURSES = 'courses';
const BOOKINGS = 'bookings';
const SYSTEM_SETTINGS = 'systemSettings';

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action, TriggerName } = event;

  console.log('定时触发器调用:', { action, TriggerName, event });

  try {
    // 根据触发器名称执行对应的任务
    if (TriggerName) {
      switch (TriggerName) {
        case 'dailyReminder':
          console.log('执行每日提醒任务');
          return await sendDailyReminder();
        case 'courseReminder':
          console.log('执行课程提醒任务');
          return await sendCourseReminder();
        case 'cleanExpiredNotifications':
          console.log('执行清理过期通知任务');
          return await cleanExpiredNotifications();
        default:
          console.log('未知触发器:', TriggerName);
          return {
            success: false,
            message: `未知触发器: ${TriggerName}`
          };
      }
    }

    // 兼容手动调用的情况
    switch (action) {
      case 'dailyReminder':
        return await sendDailyReminder();
      case 'courseReminder':
        return await sendCourseReminder();
      case 'cleanExpiredNotifications':
        return await cleanExpiredNotifications();
      default:
        return {
          success: false,
          message: '请指定action参数或通过定时触发器调用'
        };
    }
  } catch (error) {
    console.error('通知调度任务失败:', error);
    return {
      success: false,
      message: '调度任务失败',
      error: error.message
    };
  }
};

/**
 * 发送每日课程提醒（每天晚上20:00执行）
 * 提醒明天有课程的学员和讲师
 */
async function sendDailyReminder() {
  try {
    console.log('开始执行每日提醒任务...');
    
    // 计算明天的日期范围
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999);
    
    // 查询明天的课程
    const { data: tomorrowCourses } = await db.collection(COURSES).where({
      status: 'online',
      startTime: _.gte(tomorrow).and(_.lte(tomorrowEnd))
    }).get();
    
    if (tomorrowCourses.length === 0) {
      return {
        success: true,
        message: '明天没有课程，无需发送提醒',
        data: { courseCount: 0, notificationCount: 0 }
      };
    }
    
    console.log(`找到明天的课程 ${tomorrowCourses.length} 个`);
    
    const notifications = [];
    
    for (const course of tomorrowCourses) {
      // 获取课程的预约记录
      const { data: bookings } = await db.collection(BOOKINGS).where({
        courseId: course._id,
        status: 'upcoming'
      }).get();
      
      if (bookings.length === 0) {
        continue; // 没有预约的课程跳过
      }
      
      // 获取讲师信息
      const coachOpenids = Array.isArray(course.coach) ? course.coach : [course.coach];
      const { data: coaches } = await db.collection(USERS).where({
        openid: _.in(coachOpenids)
      }).get();
      
      // 获取学员信息
      const studentIds = bookings.map(b => b.userId);
      const { data: students } = await db.collection(USERS).where({
        openid: _.in(studentIds)
      }).get();
      
      // 格式化时间（转换为北京时间显示）
      const formatTime = (date) => {
        const d = new Date(date);
        // 转换为北京时间（UTC+8）
        const beijingTime = new Date(d.getTime() + 8 * 60 * 60 * 1000);
        const endTime = new Date(course.endTime);
        const beijingEndTime = new Date(endTime.getTime() + 8 * 60 * 60 * 1000);

        return `${beijingTime.getUTCHours().toString().padStart(2, '0')}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}-${beijingEndTime.getUTCHours().toString().padStart(2, '0')}:${beijingEndTime.getUTCMinutes().toString().padStart(2, '0')}`;
      };
      
      const timeStr = formatTime(course.startTime);
      const coachNames = coaches.map(c => c.nickName || c.openid.slice(-4)).join('、');
      const studentNames = students.map(s => s.nickName || s.openid.slice(-4)).join('、');
      
      // 给学员发送提醒
      for (const student of students) {
        notifications.push({
          recipientId: student.openid,
          type: 'daily_reminder_student',
          title: '明日课程提醒',
          content: `您明天有1节课程：《${course.name}》，讲师：${coachNames}，时间：${timeStr}，请准时参加。`,
          courseId: course._id
        });
      }
      
      // 给讲师发送提醒
      for (const coach of coaches) {
        notifications.push({
          recipientId: coach.openid,
          type: 'daily_reminder_coach',
          title: '明日课程提醒',
          content: `您明天有1节课程：《${course.name}》，时间：${timeStr}，学员：${studentNames}。`,
          courseId: course._id
        });
      }
    }
    
    // 批量创建通知
    const results = [];
    for (const notification of notifications) {
      try {
        const result = await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'createNotification',
            data: notification
          }
        });
        results.push(result.result);
      } catch (error) {
        console.error('创建每日提醒通知失败:', error);
        results.push({ success: false, error: error.message });
      }
    }
    
    console.log(`每日提醒任务完成，发送通知 ${notifications.length} 条`);
    
    return {
      success: true,
      message: '每日提醒发送完成',
      data: {
        courseCount: tomorrowCourses.length,
        notificationCount: notifications.length,
        results
      }
    };
  } catch (error) {
    console.error('每日提醒任务失败:', error);
    return {
      success: false,
      message: '每日提醒任务失败',
      error: error.message
    };
  }
}

/**
 * 发送课程开始前提醒（每小时检查一次）
 * 根据系统设置的提醒时间发送提醒
 */
async function sendCourseReminder() {
  try {
    console.log('开始执行课程提醒任务...');
    
    // 获取系统设置中的提醒时间
    const { data: systemSettings } = await db.collection(SYSTEM_SETTINGS)
      .doc('system_settings')
      .get();
    
    const reminderHours = systemSettings?.notification?.reminderHours || 3; // 默认3小时
    
    // 计算提醒时间范围
    const now = new Date();
    const reminderTime = new Date(now.getTime() + reminderHours * 60 * 60 * 1000);
    const reminderTimeEnd = new Date(reminderTime.getTime() + 60 * 60 * 1000); // 1小时窗口
    
    // 查询需要提醒的课程
    const { data: upcomingCourses } = await db.collection(COURSES).where({
      status: 'online',
      startTime: _.gte(reminderTime).and(_.lt(reminderTimeEnd))
    }).get();
    
    if (upcomingCourses.length === 0) {
      return {
        success: true,
        message: `${reminderHours}小时后没有课程，无需发送提醒`,
        data: { courseCount: 0, notificationCount: 0 }
      };
    }
    
    console.log(`找到 ${reminderHours} 小时后的课程 ${upcomingCourses.length} 个`);
    
    const notifications = [];
    
    for (const course of upcomingCourses) {
      // 获取课程的预约记录
      const { data: bookings } = await db.collection(BOOKINGS).where({
        courseId: course._id,
        status: 'upcoming'
      }).get();
      
      if (bookings.length === 0) {
        continue; // 没有预约的课程跳过
      }
      
      // 获取讲师信息
      const coachOpenids = Array.isArray(course.coach) ? course.coach : [course.coach];
      const { data: coaches } = await db.collection(USERS).where({
        openid: _.in(coachOpenids)
      }).get();
      
      // 获取学员信息
      const studentIds = bookings.map(b => b.userId);
      const { data: students } = await db.collection(USERS).where({
        openid: _.in(studentIds)
      }).get();
      
      // 格式化时间（转换为北京时间显示）
      const formatTime = (date) => {
        const d = new Date(date);
        // 转换为北京时间（UTC+8）
        const beijingTime = new Date(d.getTime() + 8 * 60 * 60 * 1000);
        const endTime = new Date(course.endTime);
        const beijingEndTime = new Date(endTime.getTime() + 8 * 60 * 60 * 1000);

        return `${beijingTime.getUTCHours().toString().padStart(2, '0')}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}-${beijingEndTime.getUTCHours().toString().padStart(2, '0')}:${beijingEndTime.getUTCMinutes().toString().padStart(2, '0')}`;
      };
      
      const timeStr = formatTime(course.startTime);
      const coachNames = coaches.map(c => c.nickName || c.openid.slice(-4)).join('、');
      const studentNames = students.map(s => s.nickName || s.openid.slice(-4)).join('、');
      
      // 给学员发送提醒
      for (const student of students) {
        notifications.push({
          recipientId: student.openid,
          type: 'course_reminder_student',
          title: '课程即将开始',
          content: `您预约的《${course.name}》将在${reminderHours}小时后开始，讲师：${coachNames}，时间：${timeStr}，请准时参加。`,
          courseId: course._id
        });
      }
      
      // 给讲师发送提醒
      for (const coach of coaches) {
        notifications.push({
          recipientId: coach.openid,
          type: 'course_reminder_coach',
          title: '课程即将开始',
          content: `您的课程《${course.name}》将在${reminderHours}小时后开始，时间：${timeStr}，学员：${studentNames}。`,
          courseId: course._id
        });
      }
    }
    
    // 批量创建通知
    const results = [];
    for (const notification of notifications) {
      try {
        const result = await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'createNotification',
            data: notification
          }
        });
        results.push(result.result);
      } catch (error) {
        console.error('创建课程提醒通知失败:', error);
        results.push({ success: false, error: error.message });
      }
    }
    
    console.log(`课程提醒任务完成，发送通知 ${notifications.length} 条`);
    
    return {
      success: true,
      message: '课程提醒发送完成',
      data: {
        reminderHours,
        courseCount: upcomingCourses.length,
        notificationCount: notifications.length,
        results
      }
    };
  } catch (error) {
    console.error('课程提醒任务失败:', error);
    return {
      success: false,
      message: '课程提醒任务失败',
      error: error.message
    };
  }
}

/**
 * 清理过期通知
 * 删除30天前的过期通知
 */
async function cleanExpiredNotifications() {
  try {
    console.log('开始执行过期通知清理任务...');

    const result = await cloud.callFunction({
      name: 'notificationManagement',
      data: {
        action: 'cleanExpiredNotifications'
      }
    });

    console.log('过期通知清理任务完成:', result.result);

    return result.result;
  } catch (error) {
    console.error('过期通知清理任务失败:', error);
    return {
      success: false,
      message: '过期通知清理任务失败',
      error: error.message
    };
  }
}
