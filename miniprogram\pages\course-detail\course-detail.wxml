<!--course-detail.wxml-->
<!-- 加载状态 -->
<view wx:if="{{loading && !courseDetail}}" class="loading-container">
  <view class="loading-content">
    <t-loading size="40rpx" text="加载中..." />
  </view>
</view>

<!-- 活动详情内容 -->
<view class="container" wx:elif="{{courseDetail}}">
  <!-- 活动图片 -->
  <view class="course-image-container" wx:if="{{courseImages.length > 0}}">
    <!-- 单张图片显示 -->
    <view wx:if="{{courseImages.length === 1}}" class="single-image-container">
      <image
        src="{{courseImages[0].tempFileURL}}"
        mode="widthFix"
        class="course-image single-image"
        bindtap="onPreviewCourseImage"
        data-index="0"
        bindload="onImageLoad"
      />
    </view>

    <!-- 多张图片轮播显示 -->
    <view wx:else class="multiple-images-container">
      <swiper
        class="course-swiper"
        style="height: {{swiperHeight}}px;"
        indicator-dots="{{true}}"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#fff"
        circular="{{true}}"
        autoplay="{{false}}"
      >
        <swiper-item
          wx:for="{{courseImages}}"
          wx:key="fileID"
          bindtap="onPreviewCourseImage"
          data-index="{{index}}"
        >
          <image
            src="{{item.tempFileURL}}"
            mode="widthFix"
            class="course-image swiper-image"
            bindload="onSwiperImageLoad"
            data-index="{{index}}"
          />
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!-- 活动标题和标签 -->
  <view class="course-header">
    <view class="course-title">{{courseDetail.name}}</view>
    <view class="course-id" bind:tap="copyCourseId">
      <text class="id-text">ID: {{courseDetail.id}}</text>
      <t-icon name="copy" size="12" />
    </view>
    <!-- 删除难度标签 -->
    <!-- <view class="course-tags">
      <t-tag theme="success" variant="light" size="small">{{courseDetail.difficulty}}</t-tag>
      <t-tag theme="warning" variant="light" size="small">{{courseStatus}}</t-tag>
    </view> -->
  </view>

  <!-- 时间与地点 -->
  <view class="detail-section">
    <view class="section-title">
      <t-icon name="time" size="20" />
      <text>时间与地点</text>
    </view>
    <view class="section-content">
      <view class="info-item">
        <t-icon name="calendar" size="16" />
        <text>{{courseDetail.date}}</text>
      </view>
      <view class="info-item">
        <t-icon name="time" size="16" />
        <text>{{courseDetail.time}}（{{courseDetail.duration}}）</text>
      </view>
      <view class="info-item">
        <t-icon name="location" size="16" />
        <text>{{courseDetail.venue}}</text>
      </view>
    </view>
  </view>

    <!-- 活动详情展示 -->
    <view class="detail-section">
      <view class="section-title">
        <t-icon name="help-circle" size="20" />
        <text>活动详情</text>
      </view>
      <view class="section-content">
        <text>{{courseDetail.activityDetail.description}}</text>
      </view>
    </view>

  <!-- 授课讲师 -->
  <view class="detail-section" wx:if="{{courseDetail.coaches && courseDetail.coaches.length > 0}}">
    <view class="section-title">
      <t-icon name="user" size="20" />
      <text>授课讲师</text>
    </view>
    <view class="section-content">
      <view class="coach-item" wx:for="{{courseDetail.coaches}}" wx:key="id">
        <t-avatar 
          image="{{item.avatar || 'cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg'}}" 
          size="40px"
          binderror="onAvatarError"
          bindload="onAvatarLoad"
        ></t-avatar>
        <view class="coach-info">
          <view class="coach-name">{{item.name}}</view>
          <view class="coach-specialty">{{item.specialty}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 预约信息 -->
  <view class="detail-section">
    <view class="section-title">
      <t-icon name="calendar" size="20" />
      <text>预约信息</text>
    </view>
    <view class="section-content">
      <view class="info-item">
        <t-icon name="chart" size="16" />
        <text>剩余名额：{{courseDetail.remaining}}/{{courseDetail.capacity}}</text>
      </view>
      <view class="info-item">
        <t-icon name="user" size="16" />
        <text>已预约：{{courseDetail.bookedCount}}人</text>
      </view>
      <view class="info-item">
        <t-icon name="close" size="16" />
        <text>可取消：{{courseDetail.cancelPolicy}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <!-- 学员身份：显示预约/取消预约按钮 -->
    <block wx:if="{{isStudent}}">
      <!-- 已预约状态且课程未结束：显示取消预约按钮 -->
      <t-button 
        wx:if="{{isBooked && courseStatus !== '已结束'}}"
        theme="danger" 
        size="large"
        block
        bind:tap="cancelBooking"
      >
        取消预约
      </t-button>
      <!-- 可预约状态：显示预约按钮 -->
      <t-button 
        wx:elif="{{courseDetail.available && courseStatus !== '已结束'}}"
        theme="primary" 
        size="large"
        block
        bind:tap="bookCourse"
      >
        立即预约
      </t-button>
      <!-- 其他状态：显示状态按钮 -->
      <t-button 
        wx:else
        theme="default" 
        size="large"
        block
        disabled
      >
        {{courseStatus}}
      </t-button>
    </block>

    <!-- 讲师身份：显示预约学员名单按钮 -->
    <block wx:elif="{{isCoach}}">
      <t-button 
        theme="primary" 
        size="large"
        block
        bind:tap="showStudentList"
      >
        查看预约学员名单
      </t-button>
    </block>

    <!-- 管理员身份：显示管理操作按钮 -->
    <block wx:elif="{{isAdmin}}">
      <view class="admin-actions">
        <t-button 
          theme="primary" 
          size="large"
          bind:tap="showStudentList"
          class="admin-btn"
        >
          预约学员名单
        </t-button>
        <t-button 
          theme="default" 
          size="large"
          bind:tap="editCourse"
          class="admin-btn"
        >
          编辑课程
        </t-button>
        <t-button 
          theme="danger" 
          size="large"
          bind:tap="deleteCourse"
          class="admin-btn"
        >
          删除课程
        </t-button>
      </view>
    </block>

    <!-- 未登录或其他身份：显示登录提示 -->
    <block wx:else>
      <t-button 
        theme="primary" 
        size="large"
        block
        bind:tap="goToLogin"
      >
        登录后操作
      </t-button>
    </block>
  </view>
</view>

<!-- 错误状态 -->
<view wx:elif="{{loadingError}}" class="error-container">
  <view class="error-icon-placeholder"></view>
  <view class="error-message">课程加载失败</view>
  <t-button theme="primary" size="large" bind:tap="onRetry">重新加载</t-button>
</view>

<!-- 预约学员名单弹窗 -->
<t-drawer 
  visible="{{showStudentList}}" 
  placement="bottom" 
  bind:close="closeStudentList"
  height="60%"
>
  <view class="student-list-container">
    <view class="student-list-header">
      <text class="student-list-title">预约学员名单</text>
      <text class="student-count">共{{studentList.length}}人</text>
    </view>
    
    <view class="student-list-content">
      <view wx:if="{{studentList.length === 0}}" class="empty-student-list">
        <t-icon name="user" size="48" />
        <text>暂无预约学员</text>
      </view>
      
      <block wx:else>
        <view class="student-item" wx:for="{{studentList}}" wx:key="id">
          <t-avatar 
            image="{{item.avatar || 'cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg'}}" 
            size="40px"
            binderror="onAvatarError"
            bindload="onAvatarLoad"
          ></t-avatar>
          <view class="student-info">
            <view class="student-name">{{item.name}}</view>
            <view class="student-book-time">预约时间：{{item.bookTime}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>
</t-drawer>

<!-- TDesign Toast 组件 -->
<t-toast id="t-toast" />

<!-- TDesign Loading 组件 -->
<t-loading id="t-loading" />

<!-- 页面级Loading -->
<t-loading wx:if="{{loading && !courseDetail}}" />