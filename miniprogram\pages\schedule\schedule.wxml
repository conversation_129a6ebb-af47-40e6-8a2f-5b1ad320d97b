<!--schedule.wxml-->
<!--
  课程表页面结构文件
  这是小程序的课程表页面，负责展示活动列表和预约功能

  页面功能：
  1. 视图切换：按日筛选、当前活动、历史活动三种视图
  2. 搜索功能：支持按课程名称、讲师姓名、场地搜索
  3. 活动列表：展示活动详情、预约状态、操作按钮
  4. 预约管理：支持预约、取消预约操作
  5. 分页加载：历史活动支持懒加载优化性能

  页面设计模式：
  - Tab切换 + 搜索 + 列表的经典布局
  - 类似于电商应用的商品列表页面
  - 类似于社交应用的动态列表页面
-->

<!-- 根容器 -->
<view class="container">
  <!--
    视图切换栏区域
    提供三种不同的数据视图切换功能
  -->
  <view class="view-section">
    <!--
      预约标签页容器
      包含三个视图切换按钮的容器
    -->
    <view class="booking-tabs">
      <!--
        视图切换标签项
        使用列表渲染创建三个切换按钮

        动态CSS类名技巧：
        class="booking-tab {{activeView === item.value ? 'active' : ''}}"
        - booking-tab: 基础样式类名
        - 条件类名：当当前激活视图等于当前项值时，添加'active'类名
        - 这是实现Tab高亮效果的常用方法

        数据传递：
        - data-value="{{item.value}}": 将视图值传递给事件处理函数
        - 在JS中通过e.currentTarget.dataset.value获取

        事件绑定：
        - bind:tap="onViewChange": 点击时调用视图切换方法
      -->
      <view
        class="booking-tab {{activeView === item.value ? 'active' : ''}}"
        wx:for="{{viewTabs}}"
        wx:key="value"
        bind:tap="onViewChange"
        data-value="{{item.value}}"
      >
        <!-- 显示标签文字 -->
        {{item.label}}
      </view>
    </view>

    <!--
      搜索框区域（条件显示）
      只在"当前活动"视图下显示搜索功能

      条件渲染说明：
      wx:if="{{activeView === 'current'}}"
      - 只有当前视图是'current'时才显示搜索框
      - 这样可以避免在不需要搜索的视图下显示多余的UI元素
      - 提升用户体验，界面更简洁

      内联样式：
      style="margin-bottom: 12px;"
      - 直接在元素上设置CSS样式
      - 12px的下边距，与下方内容保持适当间距
      - 虽然内联样式不是最佳实践，但对于简单的间距调整是可以接受的
    -->
    <view wx:if="{{activeView === 'current'}}" style="margin-bottom: 12px;">
      <!--
        搜索组件
        t-search: TDesign的搜索框组件，支持搜索建议功能

        属性详解：
        1. value="{{searchValue}}": 双向数据绑定搜索值
        2. placeholder: 占位符文字，提示用户可以搜索的内容
        3. result-list="{{searchResultList}}": 搜索建议列表
           - 当用户输入时，可以显示相关的搜索建议
           - 类似于百度搜索的下拉提示功能

        事件绑定：
        - bind:change: 输入内容改变时触发（实时搜索）
        - bind:clear: 点击清空按钮时触发
        - bind:submit: 提交搜索时触发（点击搜索按钮或回车）
        - bind:suggestion-click: 点击搜索建议项时触发

        搜索功能设计：
        1. 实时搜索：用户输入时立即显示结果
        2. 搜索建议：根据历史搜索或热门搜索显示建议
        3. 多字段搜索：支持课程名、讲师名、场地等多个字段
      -->
      <t-search
        value="{{searchValue}}"
        placeholder="搜索课程/讲师/场地"
        result-list="{{searchResultList}}"
        bind:change="onSearchChange"
        bind:clear="onSearchClear"
        bind:submit="onSearchSubmit"
        bind:suggestion-click="onSearchSuggestionTap"
      />
    </view>

  </view> 


  <!-- 横向日期选择器 - 仅在按日筛选视图下显示 -->
  <scroll-view wx:if="{{activeView === 'byDate'}}" class="date-tabs-scroll" scroll-x="true">
    <view class="date-tab"
          wx:for="{{dateTabs}}"
          wx:key="value"
          data-value="{{item.value}}"
          bindtap="onDateTabChange"
          style="color:{{selectedDate === item.value ? '#222' : '#bbb'}};font-weight:{{selectedDate === item.value ? 'bold' : 'normal'}}">
      <view class="tab-label">{{item.label}}</view>
      <view class="tab-date">{{item.date}}</view>
      <view wx:if="{{selectedDate === item.value}}" style="height:3px;background:#0052d9;border-radius:2px;margin-top:2px;"></view>
    </view>
  </scroll-view>

  <!-- 活动列表 -->
  <view class="course-list">
    <t-empty wx:if="{{activeView === 'history' && historyCourses.length === 0}}" description="{{emptyDescription}}" />
    <scroll-view
      wx:if="{{activeView === 'history' && historyCourses.length > 0}}"
      scroll-y="true"
      style="flex: 1; min-height: 0;"
      bindscrolltolower="onReachBottom"
    >
      <block wx:for="{{historyCourses}}" wx:key="id">
        <!-- 日期分组分隔条 -->
        <view wx:if="{{index === 0 || historyCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view class="course-card{{flashHistoryIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}" bind:tap="goToCourseDetail" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <view class="course-status ended">已结束</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
        </view>
      </block>
      <!-- 加载状态指示器 -->
      <view wx:if="{{historyLoading}}" class="loading-indicator">
        <view class="loading-dots">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text>正在加载历史活动...</text>
      </view>
      <view wx:elif="{{!historyHasMore}}" class="end-indicator">没有更多了</view>
      <view wx:else class="end-indicator">上拉加载更多</view>
    </scroll-view>
    <!-- 当前活动视图 - 时间轴分页模式 -->
    <view wx:elif="{{activeView === 'current'}}" style="flex: 1; display: flex; flex-direction: column;">
      <t-empty wx:if="{{visibleCurrentCourses.length === 0}}" description="{{emptyDescription}}" />
      <scroll-view
        wx:else
        scroll-y="true"
        style="flex: 1; min-height: 0;"
        bindscrolltolower="onCurrentScrollToLower"
      >
        <block wx:for="{{visibleCurrentCourses}}" wx:key="id">
          <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
          <view wx:if="{{index === 0 || visibleCurrentCourses[index-1].timelineDate !== item.timelineDate}}" class="timeline-date">{{item.timelineDate}}</view>
          <view class="course-card{{flashCurrentIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}" bind:tap="goToCourseDetail" data-course="{{item}}">
            <view class="course-header">
              <view class="course-title">{{item.name}}</view>
              <!-- 优化状态显示逻辑 -->
              <view class="course-status {{item.ended ? 'ended' : (item.isBooked ? 'booked' : (item.available ? 'available' : 'full'))}}">
                {{ item.ended ? '已结束' : (item.isBooked ? '已预约' : (item.available ? '可预约' : '已满')) }}
              </view>
            </view>
            <view class="course-info-list">
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text>{{item.formattedDate}} {{item.time}}</text>
              </view>
              <view class="info-item">
                <t-icon name="user" size="16" />
                <text>{{item.coach}}</text>
              </view>
              <view class="info-item">
                <t-icon name="location" size="16" />
                <text>{{item.venue}}</text>
              </view>
              <view class="info-item">
                <t-icon name="chart" size="16" />
                <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
              </view>
            </view>
            <view class="course-footer">
              <view class="action-buttons">
                <!-- 已结束：显示禁用按钮 -->
                <t-button
                  wx:if="{{item.ended}}"
                  size="small"
                  theme="default"
                  disabled
                >
                  已结束
                </t-button>
                <!-- 已开始：显示禁用按钮 -->
                <t-button
                  wx:elif="{{item.started}}"
                  size="small"
                  theme="default"
                  disabled
                >
                  已开始
                </t-button>
                <!-- 已预约且未开始：显示取消按钮 -->
                <t-button
                  wx:elif="{{item.isBooked}}"
                  size="small"
                  theme="danger"
                  catch:tap="cancelBooking"
                  data-course="{{item}}"
                >
                  取消预约
                </t-button>
                <!-- 可预约状态：显示预约按钮 -->
                <t-button
                  wx:elif="{{item.available}}"
                  size="small"
                  theme="primary"
                  catch:tap="bookCourse"
                  data-course="{{item}}"
                >
                  预约
                </t-button>
                <!-- 其他情况：已满 -->
                <t-button
                  wx:else
                  size="small"
                  theme="default"
                  disabled
                >
                  已满
                </t-button>
              </view>
            </view>
          </view>
        </block>
        <!-- 加载状态指示器 -->
        <view wx:if="{{isLoadingCurrentBottom}}" class="loading-indicator">
          <view class="loading-dots">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
          <text>正在加载更多活动...</text>
        </view>
        <view wx:elif="{{!noMoreCurrent}}" class="end-indicator">上拉加载更多</view>
        <view wx:else class="end-indicator">没有更多了</view>
      </scroll-view>
    </view>

    <!-- 其他视图保持原有渲染 -->
    <view wx:elif="{{activeView !== 'history' && activeView !== 'current'}}" style="flex: 1; display: flex; flex-direction: column;">
      <t-empty wx:if="{{filteredCourseList.length === 0}}" description="{{emptyDescription}}" />
      <scroll-view wx:else scroll-y="true" style="flex: 1; min-height: 0;">
        <view
          class="course-card"
          wx:for="{{filteredCourseList}}"
          wx:key="id"
          bind:tap="goToCourseDetail"
          data-course="{{item}}"
        >
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <!-- 优化状态显示逻辑 -->
            <view class="course-status {{item.ended ? 'ended' : (item.isBooked ? 'booked' : (item.available ? 'available' : 'full'))}}">
              {{ item.ended ? '已结束' : (item.isBooked ? '已预约' : (item.available ? '可预约' : '已满')) }}
            </view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
          <view class="course-footer">
            <view class="action-buttons">
              <!-- 已结束：显示禁用按钮 -->
              <t-button
                wx:if="{{item.ended}}"
                size="small"
                theme="default"
                disabled
              >
                已结束
              </t-button>
              <!-- 已开始：显示禁用按钮 -->
              <t-button
                wx:elif="{{item.started}}"
                size="small"
                theme="default"
                disabled
              >
                已开始
              </t-button>
              <!-- 已预约且未开始：显示取消按钮 -->
              <t-button
                wx:elif="{{item.isBooked}}"
                size="small"
                theme="danger"
                catch:tap="cancelBooking"
                data-course="{{item}}"
              >
                取消预约
              </t-button>
              <!-- 可预约状态：显示预约按钮 -->
              <t-button
                wx:elif="{{item.available}}"
                size="small"
                theme="primary"
                catch:tap="bookCourse"
                data-course="{{item}}"
              >
                预约
              </t-button>
              <!-- 其他情况：已满 -->
              <t-button
                wx:else
                size="small"
                theme="default"
                disabled
              >
                已满
              </t-button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
<!-- 移除自定义弹窗，使用系统默认弹窗 -->
<t-toast id="t-toast" />
</view> 
