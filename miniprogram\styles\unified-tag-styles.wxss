/* unified-tag-styles.wxss */
/* 统一的标签样式，适用于所有页面的状态标签 */

/* 基础标签样式 */
.course-status,
.booking-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 可预约状态 */
.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

/* 已预约状态 */
.course-status.booked,
.booking-status.upcoming {
  background-color: #e6f3ff;
  color: #0052d9;
}

/* 已满状态 */
.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

/* 已结束状态 */
.course-status.ended,
.booking-status.completed {
  background-color: #f0f0f0;
  color: #888;
}

/* 进行中状态 */
.booking-status.ongoing {
  background-color: #e8f5e8;
  color: #52c41a;
}

/* 已取消状态 */
.booking-status.cancelled {
  background-color: #fff2e8;
  color: #fa8c16;
}

/* 管理页面特有状态 */
.course-status.online {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.offline {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-status.no-status {
  background-color: #f0f0f0;
  color: #999;
}

/* TDesign Tag 组件的统一样式 */
.t-tag {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 考勤卡状态颜色规范 */
.t-tag--success {
  /* 有效状态 - 绿色 */
  background-color: #e8f5e8;
  color: #52c41a;
}

.t-tag--warning {
  /* 即将到期状态 - 橙色 */
  background-color: #fff2e8;
  color: #fa8c16;
}

.t-tag--danger {
  /* 已过期状态 - 红色 */
  background-color: #fff1f0;
  color: #ff4d4f;
}

/* 确保所有页面的标签样式一致 */
.t-tag--small {
  padding: 4px 8px;
  font-size: 12px;
}

.t-tag--medium {
  padding: 6px 10px;
  font-size: 14px;
}

.t-tag--large {
  padding: 8px 12px;
  font-size: 16px;
} 