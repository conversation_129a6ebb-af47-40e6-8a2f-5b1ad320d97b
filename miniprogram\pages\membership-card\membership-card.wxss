.membership-card-page {
  min-height: 100vh;
  background: #f6f6f6;
  padding-bottom: 120rpx; /* 预留底部TabBar空间 */
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 20px 16px 0 16px;
}

.membership-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 20px 16px 16px 16px;
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.card-header t-icon {
  margin-right: 10px;
  color: #0052d9;
}

.card-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-right: 12px;
}

.card-header t-tag {
  margin-left: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  flex-shrink: 0;
  white-space: nowrap;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-row {
  display: flex;
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.card-row text:first-child {
  min-width: 80px;
  color: #999;
} 