/* my-bookings.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 12px;
  /* padding-bottom: calc(12px + 120rpx + env(safe-area-inset-bottom)); */
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* 筛选栏容器样式 */
.filter-section {
  width: 100%;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 预约标签页样式 */
.booking-tabs {
  width: 100%;
  display: flex;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

.booking-tab {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  border-radius: 6px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  min-width: 0; /* 确保flex项目可以收缩 */
  white-space: nowrap; /* 防止文字换行 */
  transition: all 0.2s ease; /* 添加过渡效果 */
  box-sizing: border-box;
}

.booking-tab.active {
  background-color: #0052d9;
  color: white;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 预约列表样式 */
.booking-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.timeline-date {
  margin: 16px 0 8px 0;
  font-size: 15px;
  color: #0052d9;
  font-weight: bold;
  text-align: left;
  position: relative;
  padding-left: 16px;
}
.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

.booking-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: box-shadow 0.3s, background 0.3s, transform 0.3s;
}

.booking-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 从下方滑入的动画 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 15px;
  padding: 16px 0 8px 0;
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 15px;
  padding: 12px 0 8px 0;
  letter-spacing: 1px;
}

.booking-status {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

.booking-status.upcoming {
  background-color: #e6f3ff;
  color: #0052d9;
}

.booking-status.ongoing {
  background-color: #e8f5e8;
  color: #52c41a;
}

.booking-status.completed {
  background-color: #f0f0f0;
  color: #888;
}

.booking-status.cancelled {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-right: 80px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-info-list {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.booking-actions {
  display: flex;
  gap: 10px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.booking-actions t-button {
  flex: 1;
}