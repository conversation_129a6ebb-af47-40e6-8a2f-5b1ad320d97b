// adminManagement/index.js
// 管理员云函数，统一处理管理员相关操作

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const $ = db.command.aggregate;

// 权限验证函数（内联实现，避免跨云函数依赖）
async function verifyAdmin(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const isAdmin = user.roles && user.roles.includes('管理员');
    
    if (!isAdmin) {
      return {
        success: false,
        message: '权限不足，只有管理员可以执行此操作'
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

// 验证用户是否有课程管理权限（管理员或讲师）
async function verifyCoursePermission(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const roles = user.roles || [];
    const hasPermission = roles.includes('管理员') || roles.includes('讲师');
    
    if (!hasPermission) {
      return {
        success: false,
        message: '权限不足，只有管理员或讲师可以执行此操作'
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

// 数据库集合
const COURSES_COLLECTION = 'courses';
const MEMBERSHIP_CARD_COLLECTION = 'membershipCard';
const MEMBERSHIP_CARD_TEMPLATE_COLLECTION = 'membershipCardTemplate';
const USERS_COLLECTION = 'users';

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} event.data 数据
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  try {
    switch (action) {
      // 课程管理 - 使用课程管理权限（管理员或讲师）
      case 'addCourse':
      case 'updateCourse':
      case 'updateCourseStatus':
      case 'deleteCourse':
      case 'getCourseList':
      case 'getCourseDetail':
      case 'getCourseListPaged':
      case 'batchOperation':
        const courseAuthResult = await verifyCoursePermission(openid);
        if (!courseAuthResult.success) {
          return courseAuthResult;
        }
        break;
      
      // 会员卡管理、用户管理 - 需要管理员权限
      case 'createCard':
      case 'deleteCard':
      case 'updateCard':
      case 'issueCard':
      case 'reissueCard':
      case 'revokeCard':
      case 'delayCard':
      case 'freezeCard':
      case 'getCardList':
      case 'addTemplate':
      case 'updateTemplate':
      case 'deleteTemplate':
      case 'getTemplateList':
      case 'applyTemplate':
      case 'updateUserRole':
      case 'updateUserRoles':
      case 'getUserInfo':
      case 'deleteUser':
      case 'disableUser':
      case 'updateSystemSettings':
      case 'getCoachInfo':
      case 'updateCoachInfo':
        const adminAuthResult = await verifyAdmin(openid);
        if (!adminAuthResult.success) {
          return adminAuthResult;
        }
        break;
      
      // getUserList 临时不需要权限验证，方便调试
      case 'getUserList':
        // 临时跳过权限验证
        break;
      
      // 系统设置读取和数据库初始化 - 允许所有用户访问
      case 'getSystemSettings':
      case 'initDatabase':
      case 'testConnection':
      case 'updateAllUserLoginTime':
        // 不需要权限验证，所有用户都可以访问
        break;
      
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
    
    // 执行具体操作
    switch (action) {
      // 数据库初始化
      case 'initDatabase':
        return await initDatabase(data);
      
      // 课程管理
      case 'addCourse':
        return await addCourse(data);
      case 'updateCourse':
        return await updateCourse(data);
      case 'updateCourseStatus':
        return await updateCourseStatus(data);
      case 'deleteCourse':
        return await deleteCourse(data);
      case 'getCourseList':
        return await getCourseList(data);
      case 'getCourseListPaged':
        return await getCourseListPaged(data);
      case 'getCourseDetail':
        return await getCourseDetail(data);
      case 'batchOperation':
        return await batchOperation(data);

      // 会员卡管理
      case 'createCard':
        return await createCard(data);
      case 'deleteCard':
        return await deleteCard(data);
      case 'updateCard':
        return await updateCard(data);
      case 'issueCard':
        return await issueCard(data);
      case 'reissueCard':
        return await reissueCard(data);
      case 'revokeCard':
        return await revokeCard(data);
      case 'delayCard':
        return await delayCard(data);
      case 'freezeCard':
        return await freezeCard(data);
      case 'getCardList':
        return await getCardList(data);
      
      // 模板管理
      case 'addTemplate':
        return await addTemplate(data);
      case 'updateTemplate':
        return await updateTemplate(data);
      case 'deleteTemplate':
        return await deleteTemplate(data);
      case 'getTemplateList':
        return await getTemplateList(data);
      case 'applyTemplate':
        return await applyTemplate(data);
      
      // 用户管理
      case 'updateUserRole':
        return await updateUserRole(data);
      case 'updateUserRoles':
        return await updateUserRoles(data);
      case 'getUserList':
        return await getUserList(data);
      case 'getUserInfo':
        return await getUserInfo(data);
      case 'deleteUser':
        return await deleteUser(data);
      case 'disableUser':
        return await disableUser(data);
      case 'getCoachInfo':
        return await getCoachInfo(data);
      case 'updateCoachInfo':
        return await updateCoachInfo(data);
      
      // 系统设置
      case 'getSystemSettings':
        return await getSystemSettings(data);
      case 'updateSystemSettings':
        return await updateSystemSettings(data);
      case 'updateAllUserLoginTime':
        return await updateAllUserLoginTime();
      case 'testConnection':
        return {
          success: true,
          message: '云函数连接正常',
          timestamp: new Date().toISOString()
        };
      
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    return {
      success: false,
      message: '操作失败',
      error: error.message
    };
  }
};

/**
 * 更新所有用户的最后登录时间（测试用）
 */
async function updateAllUserLoginTime() {
  try {
    // 获取所有用户
    const { data: users } = await db.collection(USERS_COLLECTION).get();
    
    let updateCount = 0;
    
    for (const user of users) {
      // 检查是否有 lastLoginTime 字段
      if (!user.lastLoginTime) {
        await db.collection(USERS_COLLECTION).doc(user._id).update({
          data: {
            lastLoginTime: db.serverDate()
          }
        });
        updateCount++;
      }
    }
    
    return {
      success: true,
      message: `成功更新 ${updateCount} 个用户的最后登录时间`,
      updateCount: updateCount
    };
  } catch (error) {
    return {
      success: false,
      message: '更新用户登录时间失败',
      error: error.message
    };
  }
}

// ==================== 课程管理函数 ====================

/**
 * 添加课程
 * @param {Object} data 课程数据
 */
async function addCourse(data) {
  try {
    // 时间字段格式兼容处理
    if (data.startTime) {
      if (typeof data.startTime === 'string') {
        data.startTime = new Date(data.startTime);
      } else if (typeof data.startTime === 'number') {
        data.startTime = new Date(data.startTime);
      }
    }
    if (data.endTime) {
      if (typeof data.endTime === 'string') {
        data.endTime = new Date(data.endTime);
      } else if (typeof data.endTime === 'number') {
        data.endTime = new Date(data.endTime);
      }
    }
    // 验证必填字段（去除 description、suitableFor，只保留 activityDetail.description）
    const requiredFields = ['name', 'coach', 'startTime', 'endTime', 'venue', 'capacity', 'activityDetail'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return {
          success: false,
          message: `缺少必填字段: ${field}`
        };
      }
    }
    // 只校验 activityDetail.description 必填
    if (typeof data.activityDetail !== 'object' || !data.activityDetail.description) {
      return {
        success: false,
        message: '活动详情（activityDetail.description）不能为空'
      };
    }
    // 讲师校验优化：允许 coach 为空数组
    if (!Array.isArray(data.coach)) {
      return {
        success: false,
        message: '讲师字段格式错误'
      };
    }
    if (!(data.startTime instanceof Date)) {
      return {
        success: false,
        message: '开始时间格式错误'
      };
    }
    if (!(data.endTime instanceof Date)) {
      return {
        success: false,
        message: '结束时间格式错误'
      };
    }
    if (data.endTime <= data.startTime) {
      return {
        success: false,
        message: '结束时间必须晚于开始时间'
      };
    }
    if (typeof data.venue !== 'string' || data.venue.trim() === '') {
      return {
        success: false,
        message: '上课地点不能为空'
      };
    }
    if (typeof data.capacity !== 'number' || data.capacity < 1 || data.capacity > 999) {
      return {
        success: false,
        message: '人数上限必须是1-999之间的正整数'
      };
    }
    // 移除 description、suitableFor 字段校验和存储的注释
    // 设置默认值
    const courseData = {
      ...data,
      createTime: new Date(),
      updateTime: new Date()
    };
    const result = await db.collection(COURSES_COLLECTION).add({
      data: courseData
    });
    return {
      success: true,
      message: '课程添加成功',
      data: {
        _id: result._id
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '添加课程失败',
      error: error.message
    };
  }
}

/**
 * 更新课程状态
 * @param {Object} data 课程状态数据
 */
async function updateCourseStatus(data) {
  try {
    const { courseId, status, forceCancelBookings = false } = data;
    
    if (!courseId) {
      return {
        success: false,
        message: '缺少课程ID'
      };
    }
    
    if (!status || !['online', 'offline'].includes(status)) {
      return {
        success: false,
        message: '状态值无效，必须是 online 或 offline'
      };
    }
    
    // 先获取课程信息，检查是否已有status字段
    const courseResult = await db.collection(COURSES_COLLECTION).doc(courseId).get();
    
    if (!courseResult.data) {
      return {
        success: false,
        message: '课程不存在'
      };
    }
    
    const course = courseResult.data;
    
    // 如果课程没有status字段，说明是旧数据，需要添加该字段
    if (course.status === undefined) {
      console.log('为无状态课程添加status字段:', courseId);
    }
    
    let cancelledCount = 0;
    
    // 如果是下线操作且需要强制取消预约
    if (status === 'offline' && forceCancelBookings) {
      console.log('开始查询课程预约，课程ID:', courseId);
      
      try {
        // 查询该课程的所有预约
        const bookingResult = await db.collection('bookings')
          .where({
            courseId: courseId,
            status: 'upcoming'
          })
          .get();
        
        console.log('查询到预约数量:', bookingResult.data.length);
        
        if (bookingResult.data.length > 0) {
          // 批量取消预约并退还会员卡次数
          const updatePromises = [];
          const cardUpdatePromises = [];
          
          for (const booking of bookingResult.data) {
            console.log('处理预约:', booking._id, '会员卡:', booking.cardNumber);
            
            // 更新预约状态为已取消
            updatePromises.push(
              db.collection('bookings').doc(booking._id).update({
                data: {
                  status: 'cancelled',
                  updateTime: new Date()
                }
              })
            );
            
            // 退还会员卡次数
            if (booking.cardNumber) {
              cardUpdatePromises.push(
                db.collection(MEMBERSHIP_CARD_COLLECTION)
                  .where({
                    cardNumber: booking.cardNumber
                  })
                  .update({
                    data: {
                      remainingTimes: db.command.inc(1),
                      updateTime: new Date()
                    }
                  })
              );
            }
          }
          
          // 执行批量更新
          console.log('执行预约状态更新，数量:', updatePromises.length);
          await Promise.all(updatePromises);
          
          console.log('执行会员卡次数退还，数量:', cardUpdatePromises.length);
          await Promise.all(cardUpdatePromises);
          
          cancelledCount = bookingResult.data.length;
          console.log(`已取消${cancelledCount}个预约并退还会员卡次数`);
        } else {
          console.log('该课程没有预约记录');
        }
      } catch (bookingError) {
        console.error('处理预约取消时出错:', bookingError);
        throw new Error(`处理预约取消失败: ${bookingError.message}`);
      }
    }
    
    // 更新课程状态
    await db.collection(COURSES_COLLECTION).doc(courseId).update({
      data: {
        status: status,
        updateTime: new Date()
      }
    });

    // 如果是下线操作，异步发送通知
    if (status === 'offline') {
      // 使用 setTimeout 确保通知发送完全异步，不阻塞主流程
      setTimeout(async () => {
        try {
          await cloud.callFunction({
            name: 'notificationManagement',
            data: {
              action: 'sendCourseOfflineNotification',
              data: {
                courseId
              }
            }
          });
        } catch (notificationError) {
          console.error('发送课程下线通知失败:', notificationError);
          // 通知发送失败不影响课程下线的结果
        }
      }, 0);
    }

    const actionText = status === 'online' ? '上线' : '下线';
    return {
      success: true,
      message: `课程${actionText}成功`,
      cancelledCount: cancelledCount
    };
  } catch (error) {
    return {
      success: false,
      message: '更新课程状态失败',
      error: error.message
    };
  }
}

/**
 * 更新课程
 * @param {Object} data 课程数据
 */
async function updateCourse(data) {
  try {
    if (!data._id) {
      return {
        success: false,
        message: '缺少课程ID'
      };
    }
    // 时间字段格式兼容处理
    if (data.startTime) {
      if (typeof data.startTime === 'string') {
        data.startTime = new Date(data.startTime);
      } else if (typeof data.startTime === 'number') {
        data.startTime = new Date(data.startTime);
      }
    }
    if (data.endTime) {
      if (typeof data.endTime === 'string') {
        data.endTime = new Date(data.endTime);
      } else if (typeof data.endTime === 'number') {
        data.endTime = new Date(data.endTime);
      }
    }
    // 讲师校验优化：允许 coach 为空数组
    if (!Array.isArray(data.coach)) {
      return {
        success: false,
        message: '讲师字段格式错误'
      };
    }
    const updateData = {
      ...data,
      updateTime: new Date()
    };
    delete updateData._id;
    delete updateData.remaining;
    delete updateData.available;
    delete updateData.bookedCount;
    await db.collection(COURSES_COLLECTION).doc(data._id).update({
      data: updateData
    });
    return {
      success: true,
      message: '课程更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新课程失败',
      error: error.message
    };
  }
}

/**
 * 删除课程
 * @param {Object} data 课程数据
 */
async function deleteCourse(data) {
  try {
    const { _id } = data;

    if (!_id) {
      return {
        success: false,
        message: '缺少课程ID'
      };
    }

    // 先查询课程是否存在
    const courseResult = await db.collection(COURSES_COLLECTION).doc(_id).get();

    if (!courseResult.data) {
      return {
        success: false,
        message: '课程不存在'
      };
    }

    const course = courseResult.data;

    // 删除课程关联的图片
    if (course.images && Array.isArray(course.images) && course.images.length > 0) {
      try {
        await cloud.deleteFile({
          fileList: course.images
        });
        console.log(`已删除课程 ${_id} 的 ${course.images.length} 张图片`);
      } catch (imageError) {
        console.error('删除课程图片失败:', imageError);
        // 图片删除失败不影响课程删除，只记录错误
      }
    }

    // 删除课程
    await db.collection(COURSES_COLLECTION).doc(_id).remove();

    return {
      success: true,
      message: '课程删除成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除课程失败',
      error: error.message
    };
  }
}

/**
 * 获取课程列表
 * @param {Object} data 查询参数
 */
async function getCourseList(data = {}) {
  try {
    let query = db.collection(COURSES_COLLECTION);
    // 构建查询条件
    const whereCondition = {};
    // 不再支持 type 筛选
    if (data.coachId) {
      whereCondition.coach = data.coachId;
    }
    // 应用查询条件
    if (Object.keys(whereCondition).length > 0) {
      query = query.where(whereCondition);
    }
    const { data: courses } = await query
      .orderBy('createTime', 'desc')
      .get();
    return {
      success: true,
      data: courses
    };
  } catch (error) {
    return {
      success: false,
      message: '获取课程列表失败',
      error: error.message
    };
  }
}

/**
 * 获取课程详情
 * @param {Object} data 课程数据
 */
async function getCourseDetail(data) {
  try {
    const { _id } = data;
    
    if (!_id) {
      return {
        success: false,
        message: '缺少课程ID'
      };
    }
    
    const { data: course } = await db.collection(COURSES_COLLECTION).doc(_id).get();
    
    if (!course) {
      return {
        success: false,
        message: '课程不存在'
      };
    }
    
    return {
      success: true,
      data: course
    };
  } catch (error) {
    return {
      success: false,
      message: '获取课程详情失败',
      error: error.message
    };
  }
}

/**
 * 分页获取活动列表，支持状态/history筛选，返回主信息+预约人数
 * @param {Object} data { page, pageSize, status, history }
 */
async function getCourseListPaged(data = {}) {
  const { page = 1, pageSize = 20, status, history } = data;
  try {
    const now = new Date();
    const dbCmd = db.command;
    let match = {};
    if (status === 'online' || status === 'offline') {
      match.status = status;
    }
    if (history === true) {
      match.endTime = dbCmd.lt(now);
    } else if (history === false) {
      match.endTime = dbCmd.gte(now);
    }
    const agg = db.collection(COURSES_COLLECTION).aggregate();
    if (Object.keys(match).length > 0) {
      agg.match(match);
    }
    agg.sort({ startTime: -1 });
    agg.skip((page - 1) * pageSize).limit(pageSize);
    agg.lookup({
      from: 'bookings',
      localField: '_id',
      foreignField: 'courseId',
      as: 'bookingsList'
    });
    agg.project({
      _id: 1,
      name: 1,
      startTime: 1,
      endTime: 1,
      status: 1,
      capacity: 1,
      coach: 1,
      venue: 1,
      activityDetail: 1,
      createTime: 1,
      updateTime: 1,
      bookedCount: $.size(
        $.filter({
          input: '$bookingsList',
          as: 'b',
          cond: $.eq(['$$b.status', 'upcoming'])
        })
      ),
      bookingsList: 1
    });
    const { list } = await agg.end();
    // 收集所有预约学员 openid 和所有讲师 openid
    let allStudentOpenids = [];
    let allCoachOpenids = [];
    (list || []).forEach(c => {
      if (Array.isArray(c.bookingsList)) {
        allStudentOpenids.push(...c.bookingsList.map(b => b.userId || b.user_id || b.userOpenid));
      }
      if (Array.isArray(c.coach)) {
        allCoachOpenids.push(...c.coach);
      }
    });
    const allOpenids = [...new Set([...allStudentOpenids, ...allCoachOpenids].filter(Boolean))];
    // 批量查用户信息
    let userMap = {};
    if (allOpenids.length > 0) {
      const userRes = await db.collection('users').where({ openid: db.command.in(allOpenids) }).get();
      userMap = userRes.data.reduce((acc, user) => {
        acc[user.openid] = user;
        return acc;
      }, {});
    }
    // 拼接每个课程的已预约学员和讲师名
    const resultList = (list || []).map(item => {
      let bookedStudents = [];
      if (Array.isArray(item.bookingsList)) {
        bookedStudents = item.bookingsList
          .filter(b => b.status === 'upcoming')
          .map(b => userMap[b.userId || b.user_id || b.userOpenid])
          .filter(Boolean);
      }
      let coachDisplay = '';
      if (Array.isArray(item.coach)) {
        coachDisplay = item.coach.map(openid => userMap[openid]?.nickName || openid?.slice(-4)).join('、');
      }
      // 计算是否已结束
      const ended = item.endTime ? new Date(item.endTime) < now : false;
      return { ...item, _id: item._id || item.id, bookedStudents, coachDisplay, ended };
    });
    return {
      success: true,
      data: resultList
    };
  } catch (error) {
    return { success: false, message: '分页获取活动失败', error: error.message };
  }
}

// ==================== 会员卡管理函数 ====================

/**
 * 创建课程卡
 * @param {Object} data 创建参数
 */
async function createCard(data) {
  try {
    const { totalTimes, validFrom, validTo } = data;
    
    if (!totalTimes || totalTimes <= 0) {
      return {
        success: false,
        message: '总次数必须大于0'
      };
    }
    
    if (!validFrom || !validTo) {
      return {
        success: false,
        message: '缺少有效期信息'
      };
    }
    
    // 生成课程卡号
    const cardNumber = generateCardNumber();
    
    // 创建课程卡
    const cardData = {
      cardNumber,
      totalTimes: Number(totalTimes),
      remainingTimes: Number(totalTimes), // 剩余次数等于总次数
      validFrom: new Date(validFrom),
      validTo: new Date(validTo),
      status: '正常',
      createTime: new Date(),
      updateTime: new Date()
    };
    
    const result = await db.collection(MEMBERSHIP_CARD_COLLECTION).add({
      data: cardData
    });
    
    return {
      success: true,
      message: '课程卡创建成功',
      data: {
        cardId: result._id,
        cardNumber
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '创建课程卡失败',
      error: error.message
    };
  }
}

/**
 * 更新课程卡
 * @param {Object} data 更新参数
 */
async function updateCard(data) {
  try {
    const { cardId, totalTimes, validFrom, validTo } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少课程卡ID'
      };
    }
    
    if (!totalTimes || totalTimes <= 0) {
      return {
        success: false,
        message: '总次数必须大于0'
      };
    }
    
    if (!validFrom || !validTo) {
      return {
        success: false,
        message: '缺少有效期信息'
      };
    }
    
    // 检查课程卡是否存在
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    if (!cardResult.data) {
      return {
        success: false,
        message: '课程卡不存在'
      };
    }
    
    const card = cardResult.data;
    
    // 如果课程卡已绑定用户，需要特殊处理
    if (card.userId) {
      // 计算剩余次数的变化
      const remainingTimesChange = totalTimes - card.totalTimes;
      const newRemainingTimes = Math.max(0, card.remainingTimes + remainingTimesChange);
      
      await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
        data: {
          totalTimes: totalTimes,
          remainingTimes: newRemainingTimes,
          validFrom: new Date(validFrom),
          validTo: new Date(validTo),
          updateTime: new Date()
        }
      });
    } else {
      // 未绑定的课程卡，直接更新
      await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
        data: {
          totalTimes: totalTimes,
          remainingTimes: totalTimes, // 未绑定的课程卡，剩余次数等于总次数
          validFrom: new Date(validFrom),
          validTo: new Date(validTo),
          updateTime: new Date()
        }
      });
    }
    
    return {
      success: true,
      message: '课程卡更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新课程卡失败',
      error: error.message
    };
  }
}

/**
 * 删除会员卡
 * @param {Object} data 数据
 */
async function deleteCard(data) {
  try {
    const { cardId } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    // 先查询会员卡是否存在
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '会员卡不存在'
      };
    }
    
    // 删除会员卡
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).remove();
    
    return {
      success: true,
      message: '会员卡删除成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除会员卡失败',
      error: error.message
    };
  }
}

/**
 * 颁发会员卡
 * @param {Object} data 数据
 */
async function issueCard(data) {
  try {
    const { cardId, userOpenid } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    if (!userOpenid) {
      return {
        success: false,
        message: '缺少用户openid'
      };
    }
    
    // 查询会员卡
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '会员卡不存在'
      };
    }
    
    const card = cardResult.data;
    
    // 检查会员卡是否已经被颁发
    if (card.userId && card.userId !== '') {
      return {
        success: false,
        message: '该会员卡已经被颁发给其他用户'
      };
    }
    
    // 查询目标用户是否存在
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: userOpenid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '目标用户不存在，请检查openid是否正确'
      };
    }
    
    const targetUser = userResult.data[0];
    
    // 检查该用户是否已经有有效的会员卡
    const existingCardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).where({
      userId: userOpenid,
      status: '正常',
      validTo: db.command.gte(new Date())
    }).get();
    
    if (existingCardResult.data.length > 0) {
      return {
        success: false,
        message: '该用户已有有效的会员卡，不能重复颁发'
      };
    }
    
    // 更新会员卡信息：关联用户、设置颁发时间
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
      data: {
        userId: userOpenid,
        userNickName: targetUser.nickName || '未知用户',
        issueDate: new Date(),
        status: '正常'
      }
    });
    
    return {
      success: true,
      message: `会员卡已成功颁发给用户：${targetUser.nickName || '未知用户'}`
    };
  } catch (error) {
    return {
      success: false,
      message: '颁发会员卡失败',
      error: error.message
    };
  }
}

/**
 * 重新颁发会员卡（延长有效期）
 * @param {Object} data 数据
 */
async function reissueCard(data) {
  try {
    const { cardId } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    // 查询会员卡
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '会员卡不存在'
      };
    }
    
    const card = cardResult.data;
    
    // 检查会员卡是否已经颁发给用户
    if (!card.userId || card.userId === '') {
      return {
        success: false,
        message: '该会员卡尚未颁发给用户，请先颁发'
      };
    }
    
    // 延长有效期一年
    const newValidTo = new Date(card.validTo);
    newValidTo.setFullYear(newValidTo.getFullYear() + 1);
    
    // 更新有效期
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
      data: {
        validTo: newValidTo,
        issueDate: new Date() // 更新颁发时间
      }
    });
    
    return {
      success: true,
      message: '会员卡重新颁发成功，有效期已延长一年'
    };
  } catch (error) {
    return {
      success: false,
      message: '重新颁发会员卡失败',
      error: error.message
    };
  }
}

/**
 * 吊销会员卡（解除用户关联）
 * @param {Object} data 数据
 */
async function revokeCard(data) {
  try {
    const { cardId } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    // 查询会员卡
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '会员卡不存在'
      };
    }
    
    const card = cardResult.data;
    
    // 检查会员卡是否已经颁发给用户
    if (!card.userId || card.userId === '') {
      return {
        success: false,
        message: '该会员卡尚未颁发给用户，无需吊销'
      };
    }
    
    // 获取用户信息用于提示
    let userNickName = '未知用户';
    try {
      const userResult = await db.collection(USERS_COLLECTION).where({
        openid: card.userId
      }).get();
      
      if (userResult.data.length > 0) {
        userNickName = userResult.data[0].nickName || '未知用户';
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
    
    // 吊销：清除用户关联信息，但保留会员卡记录
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
      data: {
        userId: '',
        userNickName: '',
        issueDate: null,
        status: '正常' // 重置状态为正常，可以重新颁发
      }
    });
    
    return {
      success: true,
      message: `会员卡已成功吊销，已解除与用户"${userNickName}"的关联`
    };
  } catch (error) {
    return {
      success: false,
      message: '吊销会员卡失败',
      error: error.message
    };
  }
}

/**
 * 延期会员卡
 * @param {Object} data 数据
 */
async function delayCard(data) {
  try {
    const { cardId, delayDays = 30 } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    // 查询会员卡
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '会员卡不存在'
      };
    }
    
    const card = cardResult.data;
    const newValidTo = new Date(card.validTo);
    newValidTo.setDate(newValidTo.getDate() + delayDays);
    
    // 更新有效期
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
      data: {
        validTo: newValidTo
      }
    });
    
    return {
      success: true,
      message: '会员卡延期成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '延期会员卡失败',
      error: error.message
    };
  }
}

/**
 * 冻结会员卡
 * @param {Object} data 数据
 */
async function freezeCard(data) {
  try {
    const { cardId } = data;
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少会员卡ID'
      };
    }
    
    // 更新状态为冻结
    await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
      data: {
        status: '冻结'
      }
    });
    
    return {
      success: true,
      message: '会员卡冻结成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '冻结会员卡失败',
      error: error.message
    };
  }
}

/**
 * 获取会员卡列表
 * @param {Object} data 查询参数
 */
async function getCardList(data = {}) {
  try {
    let query = db.collection(MEMBERSHIP_CARD_COLLECTION);
    
    // 排除已删除的会员卡
    query = query.where({
      status: db.command.neq('已删除')
    });
    
    // Tab筛选
    if (data.activeTab === 'issued') {
      // 颁发：有效期开始<=今天<=有效期结束，且有userId
      const now = new Date();
      query = query.where({
        validFrom: db.command.lte(now),
        validTo: db.command.gte(now),
        userId: db.command.neq(''),
        status: db.command.neq('已删除')
      });
    } else if (data.activeTab === 'unissued') {
      // 未颁发：userId字段不存在或为空
      query = query.where({
        userId: db.command.or([db.command.exists(false), '']),
        status: db.command.neq('已删除')
      });
    } else if (data.activeTab === 'all') {
      // 全部：只显示有卡号的卡片（防止脏数据）
      query = query.where({ 
        cardNumber: db.command.neq(''),
        status: db.command.neq('已删除')
      });
    }
    
    const { data: cards } = await query
      .orderBy('issueDate', 'desc')
      .get();
    
    // 获取所有有userId的课程卡，用于查询用户信息
    const cardsWithUserId = cards.filter(card => card.userId);
    const userIds = [...new Set(cardsWithUserId.map(card => card.userId))];
    
    // 批量查询用户信息
    let userMap = {};
    if (userIds.length > 0) {
      try {
        const userQuery = db.collection(USERS_COLLECTION).where({
          openid: db.command.in(userIds)
        });
        const userRes = await userQuery.get();
        userMap = userRes.data.reduce((map, user) => {
          map[user.openid] = user.nickName;
          return map;
        }, {});
      } catch (userError) {
        console.warn('查询用户信息失败:', userError);
        // 查询用户信息失败，但不影响主要功能
      }
    }
    
    // 为每个课程卡添加用户昵称
    const cardsWithUserInfo = cards.map(card => ({
      ...card,
      userNickName: card.userId ? (userMap[card.userId] || '未知用户') : null
    }));
    
    return {
      success: true,
      data: cardsWithUserInfo
    };
  } catch (error) {
    return {
      success: false,
      message: '获取会员卡列表失败',
      error: error.message
    };
  }
}

// ==================== 用户管理函数 ====================

/**
 * 更新用户角色
 * @param {Object} data 用户数据
 */
async function updateUserRole(data) {
  try {
    const { userId, roles } = data;
    
    if (!userId || !roles) {
      return {
        success: false,
        message: '缺少用户ID或角色信息'
      };
    }
    
    // 验证角色格式
    if (!Array.isArray(roles)) {
      return {
        success: false,
        message: '角色必须是数组格式'
      };
    }
    
    // 更新用户角色
    await db.collection(USERS_COLLECTION).doc(userId).update({
      data: {
        roles: roles,
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      message: '用户角色更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新用户角色失败',
      error: error.message
    };
  }
}

/**
 * 更新用户角色
 * @param {Object} data 用户数据
 */
async function updateUserRoles(data) {
  try {
    const { userId, openid, roles } = data;
    
    if (!userId || !openid || !roles) {
      return {
        success: false,
        message: '缺少用户ID、openid或角色信息'
      };
    }
    
    // 验证角色格式
    if (!Array.isArray(roles)) {
      return {
        success: false,
        message: '角色必须是数组格式'
      };
    }
    
    // 验证用户是否存在
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    // 更新用户角色
    await db.collection(USERS_COLLECTION).doc(userId).update({
      data: {
        roles: roles,
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      message: '用户角色更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新用户角色失败',
      error: error.message
    };
  }
}

/**
 * 获取用户列表
 * @param {Object} data 查询参数
 */
async function getUserList(data = {}) {
  try {
    let query = db.collection(USERS_COLLECTION);
    
    // 构建查询条件
    const whereCondition = {};
    
    if (data.role) {
      whereCondition.roles = db.command.in([data.role]);
    }
    
    // 应用查询条件
    if (Object.keys(whereCondition).length > 0) {
      query = query.where(whereCondition);
    }
    
    // 分页参数
    const page = data.page || 1;
    const pageSize = data.pageSize || 20;
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const countResult = await query.count();
    const total = countResult.total;
    
    // 获取分页数据
    const { data: users } = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    const hasMore = skip + pageSize < total;
    
    return {
      success: true,
      data: users,
      hasMore: hasMore,
      total: total,
      page: page,
      pageSize: pageSize
    };
  } catch (error) {
    return {
      success: false,
      message: '获取用户列表失败',
      error: error.message
    };
  }
}

/**
 * 获取单个用户信息
 * @param {Object} data 查询参数
 */
async function getUserInfo(data) {
  try {
    const { openid } = data;
    
    if (!openid) {
      return {
        success: false,
        message: '缺少用户openid'
      };
    }
    
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    
    return {
      success: true,
      data: {
        openid: user.openid,
        nickName: user.nickName,
        role: user.role,
        avatarUrl: user.avatarUrl,
        createTime: user.createTime
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '获取用户信息失败',
      error: error.message
    };
  }
}

/**
 * 删除用户
 * @param {Object} data 用户数据
 */
async function deleteUser(data) {
  try {
    const { userId, openid } = data;

    if (!userId || !openid) {
      return {
        success: false,
        message: '缺少用户信息'
      };
    }

    // 验证用户是否存在
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 检查是否为管理员
    if (user.roles && user.roles.includes('管理员')) {
      return {
        success: false,
        message: '不能删除管理员用户'
      };
    }

    // 检查用户是否有会员卡
    const membershipCardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).where({
      userId: openid
    }).get();

    if (membershipCardResult.data.length > 0) {
      return {
        success: false,
        message: '该用户已关联会员卡，无法删除'
      };
    }

    // 检查用户是否有预约
    const bookingResult = await db.collection('bookings').where({
      userId: openid
    }).get();

    if (bookingResult.data.length > 0) {
      return {
        success: false,
        message: '该用户有预约记录，无法删除'
      };
    }

    // 删除用户 - 使用查询到的用户的 _id
    await db.collection(USERS_COLLECTION).doc(user._id).remove();

    return {
      success: true,
      message: '用户删除成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除用户失败',
      error: error.message
    };
  }
}

/**
 * 禁用用户
 * @param {Object} data 用户数据
 */
async function disableUser(data) {
  try {
    const { userId, openid } = data;

    if (!userId || !openid) {
      return {
        success: false,
        message: '缺少用户信息'
      };
    }

    // 验证用户是否存在
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 检查是否为管理员
    if (user.roles && user.roles.includes('管理员')) {
      return {
        success: false,
        message: '不能禁用管理员用户'
      };
    }

    // 检查用户是否有会员卡
    const membershipCardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).where({
      userId: openid
    }).get();

    if (membershipCardResult.data.length > 0) {
      return {
        success: false,
        message: '该用户已关联会员卡，无法禁用'
      };
    }

    // 检查用户是否有预约
    const bookingResult = await db.collection('bookings').where({
      userId: openid
    }).get();

    if (bookingResult.data.length > 0) {
      return {
        success: false,
        message: '该用户有预约记录，无法禁用'
      };
    }

    // 禁用用户 - 使用查询到的用户的 _id
    await db.collection(USERS_COLLECTION).doc(user._id).update({
      data: {
        status: '禁用',
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '用户禁用成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '禁用用户失败',
      error: error.message
    };
  }
}

// ==================== 系统设置函数 ====================

/**
 * 获取系统设置
 * @param {Object} data 查询参数
 */
async function getSystemSettings(data = {}) {
  try {
    const settingsCollection = db.collection('systemSettings');
    
    // 获取系统设置文档
    let result;
    try {
      result = await settingsCollection.doc('system_settings').get();
    } catch (error) {
      // 如果文档不存在，result 为 undefined
      result = { data: null };
    }
    
    if (!result.data) {
      // 如果文档不存在，创建默认设置
      const defaultSettings = {
        _id: 'system_settings',
        booking: {
          cancelTimeLimitMinutes: 180,  // 预约取消时间限制（分钟）
          maxBookingDays: 7,           // 最大预约天数
          allowOverbooking: false      // 是否允许超额预约
        },
        maintenance: {
          enabled: false,              // 维护模式开关
          message: "",                 // 维护模式提示信息
          startTime: null,             // 维护开始时间
          endTime: null                // 维护结束时间
        },
        notification: {
          reminderHours: 2,            // 课程提醒时间（小时）
          smsEnabled: false,           // 短信通知开关
          emailEnabled: true           // 邮件通知开关
        },
        contact: {
          phone: "138-8888-8888",      // 联系电话
          address: "未能获取", // 门店地址
          announcement: "未能获取" // 门店公告
        },
        updatedAt: new Date(),
        updatedBy: null
      };
      
      console.log('创建默认系统设置:', JSON.stringify(defaultSettings, null, 2));
      
      await settingsCollection.add({
        data: defaultSettings
      });
      
      return {
        success: true,
        data: defaultSettings
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      message: '获取系统设置失败',
      error: error.message
    };
  }
}

/**
 * 更新系统设置
 * @param {Object} data 设置数据
 */
async function updateSystemSettings(data) {
  try {
    console.log('updateSystemSettings 被调用，接收到的数据:', JSON.stringify(data));
    
    const { booking, maintenance, notification, contact } = data;
    
    if (!booking && !maintenance && !notification && !contact) {
      console.log('缺少设置数据，返回错误');
      return {
        success: false,
        message: '缺少设置数据'
      };
    }
    
    const settingsCollection = db.collection('systemSettings');
    
    // 获取当前设置
    let currentSettings = {};
    try {
      const currentResult = await settingsCollection.doc('system_settings').get();
      if (currentResult.data) {
        currentSettings = currentResult.data;
      }
    } catch (error) {
      // 如果文档不存在，使用空对象，后续会创建新文档
      console.log('系统设置文档不存在，将创建新文档');
    }
    
    // 合并新的设置（排除_id字段，因为更新时不能修改_id）
    const { _id, ...settingsWithoutId } = currentSettings;
    const updatedSettings = {
      ...settingsWithoutId,
      ...(booking && { booking: { ...(currentSettings.booking || {}), ...booking } }),
      ...(maintenance && { maintenance: { ...(currentSettings.maintenance || {}), ...maintenance } }),
      ...(notification && { notification: { ...(currentSettings.notification || {}), ...notification } }),
      ...(contact && { contact: { ...(currentSettings.contact || {}), ...contact } }),
      updatedAt: new Date()
    };
    
    console.log('合并后的设置:', JSON.stringify(updatedSettings, null, 2));
    
    // 更新或创建设置文档
    if (Object.keys(currentSettings).length > 0) {
      // 文档存在，更新
      console.log('更新现有系统设置文档');
      await settingsCollection.doc('system_settings').update({
        data: updatedSettings
      });
    } else {
      // 文档不存在，创建
      console.log('创建新的系统设置文档');
      await settingsCollection.add({
        data: {
          _id: 'system_settings',
          ...updatedSettings
        }
      });
    }
    
    return {
      success: true,
      message: '系统设置更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新系统设置失败',
      error: error.message
    };
  }
}

// ==================== 用户管理函数 ====================

/**
 * 获取讲师信息
 * @param {Object} data 查询参数
 */
async function getCoachInfo(data) {
  try {
    const { openid } = data;

    if (!openid) {
      return {
        success: false,
        message: '缺少讲师openid'
      };
    }

    const coachResult = await db.collection('coachInfo').where({
      openid: openid
    }).get();

    if (coachResult.data.length === 0) {
      // 如果没有找到讲师信息，返回空数据
      return {
        success: true,
        data: {
          openid: openid,
          introduction: '',
          specialties: ''
        }
      };
    }

    const coach = coachResult.data[0];

    return {
      success: true,
      data: {
        openid: coach.openid,
        introduction: coach.introduction || '',
        specialties: coach.specialties || '',
        createTime: coach.createTime,
        updateTime: coach.updateTime
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '获取讲师信息失败',
      error: error.message
    };
  }
}

/**
 * 更新讲师信息
 * @param {Object} data 讲师数据
 */
async function updateCoachInfo(data) {
  try {
    const { openid, introduction, specialties } = data;

    if (!openid) {
      return {
        success: false,
        message: '缺少讲师openid'
      };
    }

    // 验证用户是否存在
    const userResult = await db.collection(USERS_COLLECTION).where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    // 检查是否已有讲师信息
    const coachResult = await db.collection('coachInfo').where({
      openid: openid
    }).get();

    const now = new Date();

    if (coachResult.data.length === 0) {
      // 创建新的讲师信息
      await db.collection('coachInfo').add({
        data: {
          openid: openid,
          introduction: introduction || '',
          specialties: specialties || '',
          createTime: now,
          updateTime: now
        }
      });
    } else {
      // 更新现有讲师信息
      const coach = coachResult.data[0];
      await db.collection('coachInfo').doc(coach._id).update({
        data: {
          introduction: introduction || '',
          specialties: specialties || '',
          updateTime: now
        }
      });
    }

    return {
      success: true,
      message: '讲师信息更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新讲师信息失败',
      error: error.message
    };
  }
}

// ==================== 模板管理函数 ====================

/**
 * 添加模板
 * @param {Object} data 模板数据
 */
async function addTemplate(data) {
  try {
    console.log('addTemplate 被调用，接收到的数据:', JSON.stringify(data));
    
    const { name, totalTimes, validDays, showOnHomepage, description } = data;
    
    if (!name || !totalTimes || !validDays) {
      console.log('缺少必要字段:', { name, totalTimes, validDays });
      return {
        success: false,
        message: '缺少必要字段：模板名称、总次数、有效期天数'
      };
    }
    
    // 验证数据
    if (totalTimes <= 0 || validDays <= 0) {
      console.log('数据验证失败:', { totalTimes, validDays });
      return {
        success: false,
        message: '总次数和有效期天数必须大于0'
      };
    }
    
    console.log('开始检查模板名称是否已存在:', name);
    
    // 检查模板名称是否已存在
    const existingTemplate = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION)
      .where({
        name: name
      })
      .get();
    
    console.log('查询现有模板结果:', existingTemplate);
    
    if (existingTemplate.data.length > 0) {
      console.log('模板名称已存在');
      return {
        success: false,
        message: '模板名称已存在，请使用其他名称'
      };
    }
    
    // 创建模板
    const templateData = {
      name,
      totalTimes: parseInt(totalTimes),
      validDays: parseInt(validDays),
      showOnHomepage: showOnHomepage || false,
      description: description || '',
      createTime: new Date(),
      updateTime: new Date(),
      appliedCount: 0 // 应用次数
    };
    
    console.log('准备创建模板，数据:', JSON.stringify(templateData));
    
    const result = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).add({
      data: templateData
    });
    
    console.log('模板创建成功，结果:', result);
    
    return {
      success: true,
      message: '模板创建成功',
      data: {
        id: result._id,
        ...templateData
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '添加模板失败',
      error: error.message
    };
  }
}

/**
 * 更新模板
 * @param {Object} data 模板数据
 */
async function updateTemplate(data) {
  try {
    const { id, name, totalTimes, validDays, showOnHomepage, description } = data;
    
    if (!id) {
      return {
        success: false,
        message: '缺少模板ID'
      };
    }
    
    if (!name || !totalTimes || !validDays) {
      return {
        success: false,
        message: '缺少必要字段：模板名称、总次数、有效期天数'
      };
    }
    
    // 验证数据
    if (totalTimes <= 0 || validDays <= 0) {
      return {
        success: false,
        message: '总次数和有效期天数必须大于0'
      };
    }
    
    // 检查模板名称是否已被其他模板使用
    const existingTemplate = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION)
      .where({
        name: name,
        _id: db.command.neq(id)
      })
      .get();
    
    if (existingTemplate.data.length > 0) {
      return {
        success: false,
        message: '模板名称已存在，请使用其他名称'
      };
    }
    
    // 更新模板
    const updateData = {
      name,
      totalTimes: parseInt(totalTimes),
      validDays: parseInt(validDays),
      showOnHomepage: showOnHomepage || false,
      description: description || '',
      updateTime: new Date()
    };
    
    await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).update({
      data: updateData
    });
    
    return {
      success: true,
      message: '模板更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新模板失败',
      error: error.message
    };
  }
}

/**
 * 删除模板
 * @param {Object} data 删除参数
 */
async function deleteTemplate(data) {
  try {
    const { id } = data;
    
    if (!id) {
      return {
        success: false,
        message: '缺少模板ID'
      };
    }
    
    // 检查模板是否已被应用
    const template = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).get();
    if (!template.data) {
      return {
        success: false,
        message: '模板不存在'
      };
    }
    
    if (template.data.appliedCount > 0) {
      return {
        success: false,
        message: '该模板已被应用，无法删除'
      };
    }
    
    // 删除模板
    await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).remove();
    
    return {
      success: true,
      message: '模板删除成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除模板失败',
      error: error.message
    };
  }
}

/**
 * 获取模板列表
 * @param {Object} data 查询参数
 */
async function getTemplateList(data = {}) {
  try {
    let query = db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION);
    
    // Tab筛选
    if (data.activeTab === 'homepage') {
      // 首页展示：showOnHomepage为true
      query = query.where({
        showOnHomepage: true
      });
    }
    // activeTab === 'all' 时不添加筛选条件
    
    const { data: templates } = await query
      .orderBy('createTime', 'desc')
      .get();
    
    // 处理模板数据，添加显示文本
    const processedTemplates = templates.map(template => ({
      ...template,
      id: template._id,
      showOnHomepageText: template.showOnHomepage ? '是' : '否'
    }));
    
    return {
      success: true,
      data: processedTemplates
    };
  } catch (error) {
    return {
      success: false,
      message: '获取模板列表失败',
      error: error.message
    };
  }
}

/**
 * 应用模板创建课程卡
 * @param {Object} data 应用参数
 */
async function applyTemplate(data) {
  try {
    const { templateId, userId, userOpenid } = data;
    
    if (!templateId) {
      return {
        success: false,
        message: '缺少模板ID'
      };
    }
    
    // 获取模板信息
    const templateResult = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(templateId).get();
    if (!templateResult.data) {
      return {
        success: false,
        message: '模板不存在'
      };
    }
    
    const template = templateResult.data;
    
    // 生成课程卡号
    const cardNumber = generateCardNumber();
    
    // 计算有效期
    const now = new Date();
    const validFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const validTo = new Date(validFrom);
    validTo.setDate(validFrom.getDate() + template.validDays);
    
    // 创建课程卡数据
    const cardData = {
      cardNumber,
      totalTimes: template.totalTimes,
      remainingTimes: template.totalTimes,
      validFrom,
      validTo,
      status: '正常',
      templateId: templateId,
      templateName: template.name,
      createTime: now,
      updateTime: now
    };
    
    // 如果指定了用户，则绑定用户
    if (userId || userOpenid) {
      const targetOpenid = userOpenid || userId;
      
      // 验证用户是否存在
      const userResult = await db.collection(USERS_COLLECTION).where({
        openid: targetOpenid
      }).get();
      
      if (userResult.data.length === 0) {
        return {
          success: false,
          message: '用户不存在'
        };
      }
      
      const user = userResult.data[0];
      cardData.userId = targetOpenid;
      cardData.userNickName = user.nickName || '未知用户';
      cardData.issueDate = now;
    }
    
    // 创建课程卡
    const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).add({
      data: cardData
    });
    
    // 更新模板的应用次数
    await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(templateId).update({
      data: {
        appliedCount: db.command.inc(1),
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      message: '模板应用成功',
      data: {
        cardId: cardResult._id,
        cardNumber,
        templateName: template.name
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '应用模板失败',
      error: error.message
    };
  }
}

/**
 * 生成课程卡号
 */
function generateCardNumber() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CARD${timestamp.slice(-8)}${random}`;
}

/**
 * 初始化数据库集合
 * @param {Object} data 初始化参数
 */
async function initDatabase(data) {
  try {
    console.log('开始初始化数据库集合...');
    
    // 创建 membershipCardTemplate 集合
    try {
      await db.createCollection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION);
      console.log('membershipCardTemplate 集合创建成功');
    } catch (error) {
      if (error.message.includes('collection already exists')) {
        console.log('membershipCardTemplate 集合已存在');
      } else {
        console.error('创建 membershipCardTemplate 集合失败:', error);
        throw error;
      }
    }
    
    // 创建其他必要的集合（如果不存在）
    const collections = [
      'courses',
      'membershipCard',
      'users',
      'bookings',
      'systemSettings',
      'notifications'
    ];
    
    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName);
        console.log(`${collectionName} 集合创建成功`);
      } catch (error) {
        if (error.message.includes('collection already exists')) {
          console.log(`${collectionName} 集合已存在`);
        } else {
          console.error(`创建 ${collectionName} 集合失败:`, error);
        }
      }
    }
    
    return {
      success: true,
      message: '数据库集合初始化成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '初始化数据库失败',
      error: error.message
    };
  }
}

/**
 * 批量操作课程
 *
 * 功能：支持批量上线、下线、删除课程
 * 权限：需要课程管理权限（管理员或讲师）
 *
 * @param {Object} data 操作数据
 * @param {string} data.courseId 课程ID
 * @param {string} data.operationType 操作类型：'online'|'offline'|'delete'
 * @returns {Object} 操作结果
 */
async function batchOperation(data) {
  try {
    const { courseId, operationType } = data;

    if (!courseId) {
      return {
        success: false,
        message: '课程ID不能为空'
      };
    }

    if (!operationType) {
      return {
        success: false,
        message: '操作类型不能为空'
      };
    }

    // 获取课程信息
    const courseResult = await db.collection('courses').doc(courseId).get();
    if (!courseResult.data) {
      return {
        success: false,
        message: '课程不存在'
      };
    }

    const course = courseResult.data;

    // 根据操作类型执行相应操作
    switch (operationType) {
      case 'online':
        return await batchOnlineCourse(courseId, course);
      case 'offline':
        return await batchOfflineCourse(courseId, course);
      case 'delete':
        return await batchDeleteCourse(courseId, course);
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        };
    }

  } catch (error) {
    console.error('批量操作失败:', error);
    return {
      success: false,
      message: '批量操作失败',
      error: error.message
    };
  }
}

/**
 * 批量上线课程
 */
async function batchOnlineCourse(courseId, course) {
  try {
    // 检查课程是否已结束
    if (course.ended) {
      return {
        success: false,
        message: '已结束的课程无法上线'
      };
    }

    // 更新课程状态为上线
    await db.collection('courses').doc(courseId).update({
      data: {
        status: 'online',
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '课程上线成功'
    };

  } catch (error) {
    console.error('批量上线课程失败:', error);
    return {
      success: false,
      message: '课程上线失败',
      error: error.message
    };
  }
}

/**
 * 批量下线课程
 */
async function batchOfflineCourse(courseId, course) {
  try {
    // 更新课程状态为下线
    await db.collection('courses').doc(courseId).update({
      data: {
        status: 'offline',
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '课程下线成功'
    };

  } catch (error) {
    console.error('批量下线课程失败:', error);
    return {
      success: false,
      message: '课程下线失败',
      error: error.message
    };
  }
}

/**
 * 批量删除课程
 */
async function batchDeleteCourse(courseId, course) {
  try {
    // 检查课程是否有预约记录
    const bookingResult = await db.collection('bookings')
      .where({
        courseId: courseId,
        status: db.command.in(['confirmed', 'pending'])
      })
      .count();

    if (bookingResult.total > 0) {
      return {
        success: false,
        message: '该课程已有学员预约，无法删除'
      };
    }

    // 删除课程
    await db.collection('courses').doc(courseId).remove();

    return {
      success: true,
      message: '课程删除成功'
    };

  } catch (error) {
    console.error('批量删除课程失败:', error);
    return {
      success: false,
      message: '课程删除失败',
      error: error.message
    };
  }
}