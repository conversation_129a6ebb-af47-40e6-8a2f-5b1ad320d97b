/* course-detail.wxss */
.container {
  padding: 16px;
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "PingFang SC", "<PERSON>Fang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 100%;
}

/* 图片容器 */
.course-image-container {
  width: 100%;
  margin-bottom: 16px;
}

/* 单张图片容器 */
.single-image-container {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background-color: #f5f5f5;
}

/* 单张图片样式 */
.single-image {
  width: 100%;
  display: block;
  background-color: #f5f5f5;
}

/* 多张图片容器 */
.multiple-images-container {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background-color: #f5f5f5;
}

/* 轮播图样式 */
.course-swiper {
  width: 100%;
  min-height: 200px; /* 最小高度，防止闪烁 */
}

/* 轮播图项目样式 */
.course-swiper swiper-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 轮播图中的图片 */
.swiper-image {
  width: 100%;
  max-width: 100%;
  display: block;
  background-color: #f5f5f5;
}

/* 课程图片通用样式 */
.course-image {
  background-color: #f5f5f5;
}

/* 图片占位符（兜底显示） */
.course-image-placeholder {
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.placeholder-text {
  margin-top: 8px;
  font-size: 14px;
  color: #999;
}

.course-header {
  margin-bottom: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-id {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: fit-content;
}

.course-id:active {
  background-color: #e0e0e0;
}

.id-text {
  font-size: 12px;
  color: #999;
  margin-right: 4px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.course-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.detail-section {
  margin-bottom: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.section-title t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.section-content {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.course-description {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.suitable-crowd {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
  display: block;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.coach-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.coach-item:last-child {
  margin-bottom: 0;
}

/* 讲师头像样式 */
.coach-avatar-placeholder,
t-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
}

.coach-info {
  margin-left: 12px;
  flex: 1;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.coach-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.coach-specialty {
  font-size: 12px;
  color: #999;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 32px;
}

/* 错误图标占位符 */
.error-icon-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin-bottom: 32px;
}

.error-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #ffffff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 管理员操作按钮样式 */
.admin-actions {
  display: flex;
  gap: 8px;
}

.admin-btn {
  flex: 1;
}

/* 预约学员名单弹窗样式 */
.student-list-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.student-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.student-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.student-count {
  font-size: 14px;
  color: #666;
}

.student-list-content {
  flex: 1;
  overflow-y: auto;
}

.empty-student-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-student-list t-icon {
  margin-bottom: 12px;
  color: #ccc;
}

.empty-student-list text {
  font-size: 14px;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.student-item:last-child {
  border-bottom: none;
}

.student-info {
  margin-left: 12px;
  flex: 1;
}

.student-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.student-book-time {
  font-size: 12px;
  color: #999;
}