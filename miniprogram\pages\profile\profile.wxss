/* profile.wxss */
/*
 * 个人中心页面样式文件
 *
 * 设计理念：
 * 1. 卡片式设计：使用白色卡片承载内容，增强层次感
 * 2. 圆角设计：大量使用圆角元素，营造现代、友好的视觉效果
 * 3. 阴影效果：适度使用阴影增强立体感和层次
 * 4. 色彩搭配：主要使用蓝色系作为主色调，灰色作为辅助色
 * 5. 响应式布局：使用Flexbox确保在不同屏幕尺寸下的良好显示
 * 6. 单位使用：混合使用rpx和px，rpx用于响应式尺寸，px用于固定尺寸
 *
 * 页面结构：
 * - 未登录状态：居中的登录卡片
 * - 已登录状态：用户信息卡片 + 功能菜单列表
 */

/**
 * .container: 页面根容器样式
 *
 * 设计说明：
 * - 使用rpx单位实现响应式布局
 * - 底部padding特殊处理，为tabBar和安全区域预留空间
 * - 统一字体族，确保整个页面字体一致性
 *
 * 单位说明：
 * - rpx: 响应式像素，750rpx = 屏幕宽度，自动适配不同屏幕
 * - px: 固定像素，不会根据屏幕缩放
 * - calc(): CSS计算函数，支持不同单位的混合运算
 * - env(): CSS环境变量函数，获取系统安全区域信息
 */
.container {
  /* 基础内边距：32rpx响应式内边距 */
  padding: 32rpx;

  /*
   * 底部内边距特殊处理：
   * calc(32rpx + 120rpx + env(safe-area-inset-bottom))
   * - 32rpx: 基础内边距
   * - 120rpx: tabBar高度预留
   * - env(safe-area-inset-bottom): iPhone X等全面屏底部安全区域
   *
   * 这样设计确保内容不会被tabBar或安全区域遮挡
   */
  padding-bottom: calc(32rpx + 120rpx + env(safe-area-inset-bottom));

  /* 背景色：浅灰色，与白色卡片形成对比 */
  background-color: #f5f5f5;

  /* 最小高度：确保页面至少占满整个视口 */
  min-height: 100vh;

  /*
   * 字体族设置：
   * 定义了完整的字体回退链，确保在不同系统上都有良好的字体显示
   * - "PingFang SC": 苹果系统简体中文字体（iOS/macOS）
   * - "PingFang": 苹果系统字体的通用名称
   * - "Helvetica Neue": 苹果系统英文字体
   * - "Helvetica": Helvetica字体的通用版本
   * - "Arial": Windows系统常用字体
   * - sans-serif: 无衬线字体族（最终回退）
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 盒模型：padding和border包含在总宽度内 */
  box-sizing: border-box;
}

/* Logo样式 */
.logo {
  margin-right: 12px;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar {
  margin-right: 24rpx;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 登录区域样式 - 优化未登录状态 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.login-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx;
}

.login-content {
  text-align: center;
  width: 100%;
  max-width: 320px;
  padding: 40px 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 统一文字样式 */
.login-text {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 24px 0 12px 0;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.login-tip {
  font-size: 15px;
  color: #666666;
  margin-bottom: 40px;
  line-height: 1.5;
  font-weight: 400;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 登录按钮区域样式 */
.login-button-section {
  margin: 32px 0 24px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.wx-login-btn {
  border-radius: 16px;
  font-weight: 600;
  font-size: 16px;
  height: 48px;
  line-height: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
  letter-spacing: 0.5px;
}

.wx-login-btn:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 82, 217, 0.3);
}

/* 重置button默认样式 */
.wx-login-btn::after {
  border: none;
}

/* 个人资料区域样式 */
.profile-section {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 卡片样式 */
.profile-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.profile-card.student,
.profile-card.coach,
.profile-card.admin {
  border-top: none;
}
/*
  左侧色块实现说明：
  通过 .card-header 的 ::before 伪元素生成一个宽度为 8rpx 的竖色块，
  位置绝对定位于 header 左侧，高度与 header 匹配，圆角与 header 保持一致。
  这样无需额外 view 标签，结构更简洁，且易于维护和统一风格。
  不同卡片通过不同父类（如 .student/.coach/.admin/.help/.info）控制色块颜色。
*/
.profile-card.student .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #FFAA00;
  border-radius: 8rpx;
}
.profile-card.coach .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #36B37E;
  border-radius: 8rpx;
}
.profile-card.admin .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #0052D9;
  border-radius: 8rpx;
}
.profile-card.help .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #00B8D9;
  border-radius: 8rpx;
}
.profile-card.info .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #36B37E;
  border-radius: 8rpx;
}
.card-header {
  cursor: default;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 用户信息卡片头部特殊样式 - 支持右侧通知图标 */
.profile-card.info .card-header {
  justify-content: space-between; /* 两端对齐，左侧标题，右侧通知图标 */
}

/* 卡片头部左侧区域 */
.card-header-left {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 16rpx;

}

.card-content {
  padding-left: 32rpx;
  padding-right: 32rpx;
  margin-bottom: 8rpx;

}
.card-content-userprofile {
 padding: 32rpx;
}

.user-info {
  display: flex;
  align-items: center;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  padding-bottom: 32rpx;
  padding-top: 32rpx;
}

.user-details {
  margin-left: 16px;
  flex: 1;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-role {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 功能区域样式 */
.function-section {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-group {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-left: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
}

.group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #0052d9, #1890ff);
  border-radius: 2px;
}

.function-list {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: all 0.2s ease;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.function-item t-icon:first-child {
  margin-right: 12px;
  color: #0052d9;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-item text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
}

.function-item-label {
  margin-left: 16rpx;
}

.function-item t-icon:last-child {
  color: #ccc;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 退出登录区域样式 */
.logout-section {
  margin-top: 32px;
  margin-bottom: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.logout-btn {
  display: block;
  margin: 40rpx auto 0 auto;
  width: 80%;
  border-radius: 24rpx;
  font-weight: 700;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(227,77,89,0.08);
  background: #fff0f0;
  color: #e34d59;
  border: 2rpx solid #e34d59;
  transition: background 0.2s, color 0.2s;
}
.logout-btn:active {
  background: #e34d59;
  color: #fff;
}

.avatar-btn {
  padding: 0;
  border: none;
  background: none;
  outline: none;
  box-shadow: none;
  display: inline-block;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}
.avatar-btn-overlay {
  position: absolute;
  left: 0; top: 0;
  width: 60px; height: 60px;
  opacity: 0;
  padding: 0;
  border: none;
  background: none;
  z-index: 2;
}

.agreement-link {
  color: #0052d9;
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-thickness: 1px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.agreement-link:hover {
  color: #003d9e;
  text-decoration-color: #003d9e;
}

.agreement-link:active {
  color: #002a7a;
  text-decoration-color: #002a7a;
}

.agreement-section {
  background: transparent !important;
  padding: 0;
  margin-top: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.agreement-section t-checkbox,
.agreement-section .t-checkbox {
  background: transparent !important;
}

/*
  通知图标相关样式
  用于个人中心页面的通知入口
*/

/* 通知图标包装器 */
.notification-icon-wrapper {
  position: relative; /* 相对定位，为红点提供定位基准 */
  padding: 8rpx; /* 增加点击区域 */
  cursor: pointer; /* 鼠标指针样式 */
  transition: opacity 0.2s ease; /* 点击时的透明度过渡 */
}

/* 通知图标点击效果 */
.notification-icon-wrapper:active {
  opacity: 0.6; /* 点击时降低透明度，提供视觉反馈 */
}

/* 通知红点徽章 */
.notification-badge {
  position: absolute; /* 绝对定位，相对于父容器 */
  top: 0; /* 距离顶部0距离 */
  right: 0; /* 距离右侧0距离 */
  min-width: 32rpx; /* 最小宽度，确保圆形 */
  height: 32rpx; /* 固定高度 */
  background-color: #ff4757; /* 红色背景 */
  border-radius: 16rpx; /* 圆角，形成圆形或胶囊形 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  border: 2rpx solid #ffffff; /* 白色边框，与背景分离 */
  box-sizing: border-box; /* 边框计入尺寸 */
}

/* 红点内的数字文字 */
.badge-text {
  font-size: 20rpx; /* 小字体 */
  color: #ffffff; /* 白色文字 */
  font-weight: 600; /* 加粗字重 */
  line-height: 1; /* 行高1，确保垂直居中 */
  padding: 0 6rpx; /* 左右内边距，为长数字提供空间 */
  white-space: nowrap; /* 不换行 */
}

.agreement-section text {
  font-size: 14px;
  line-height: 1.5;
  color: #666666;
  font-weight: 400;
}