// 测试新的预约逻辑
// 这个脚本模拟数据库操作来验证逻辑是否正确

// 模拟数据库
let mockBookings = [];
let mockMembershipCards = [
  {
    _id: 'card1',
    userId: 'user1',
    cardNumber: 'CARD001',
    remainingTimes: 10,
    status: '正常'
  }
];

// 模拟数据库操作
const mockDB = {
  collection: (name) => ({
    where: (condition) => ({
      get: () => {
        if (name === 'bookings') {
          const filtered = mockBookings.filter(booking => {
            return Object.keys(condition).every(key => {
              if (key === 'status' && condition[key] === 'upcoming') {
                return booking.status === 'upcoming';
              }
              return booking[key] === condition[key];
            });
          });
          return Promise.resolve({ data: filtered });
        }
        if (name === 'membershipCard') {
          return Promise.resolve({ data: mockMembershipCards });
        }
        return Promise.resolve({ data: [] });
      }
    }),
    doc: (id) => ({
      update: (data) => {
        if (name === 'bookings') {
          const booking = mockBookings.find(b => b._id === id);
          if (booking) {
            Object.assign(booking, data.data);
          }
        }
        if (name === 'membershipCard') {
          const card = mockMembershipCards.find(c => c._id === id);
          if (card) {
            Object.assign(card, data.data);
          }
        }
        return Promise.resolve();
      }
    }),
    add: (data) => {
      const newId = 'booking' + (mockBookings.length + 1);
      const newBooking = { _id: newId, ...data.data };
      mockBookings.push(newBooking);
      return Promise.resolve({ _id: newId });
    }
  })
};

// 模拟新的预约逻辑
async function testBookCourse(courseId, userId) {
  console.log(`\n=== 测试预约课程 ${courseId} (用户: ${userId}) ===`);
  
  // 检查用户对该课程的预约记录（任何状态）
  const existingBooking = await mockDB.collection('bookings')
    .where({ courseId, userId }).get();
  
  console.log('现有预约记录:', existingBooking.data);
  
  // 如果已经有upcoming状态的预约，直接返回
  if (existingBooking.data.length > 0) {
    const booking = existingBooking.data[0];
    if (booking.status === 'upcoming') {
      console.log('结果: 您已预约');
      return { success: false, message: '您已预约' };
    }
  }
  
  // 模拟扣减会员卡次数
  const selectedCard = mockMembershipCards[0];
  console.log('扣减前会员卡次数:', selectedCard.remainingTimes);
  
  await mockDB.collection('membershipCard').doc(selectedCard._id).update({
    data: {
      remainingTimes: selectedCard.remainingTimes - 1,
      updateTime: new Date()
    }
  });
  
  console.log('扣减后会员卡次数:', selectedCard.remainingTimes);
  
  // 如果存在记录，更新状态；否则创建新记录
  if (existingBooking.data.length > 0) {
    console.log('更新现有记录状态为upcoming');
    const bookingId = existingBooking.data[0]._id;
    await mockDB.collection('bookings').doc(bookingId).update({
      data: {
        status: 'upcoming',
        cardNumber: selectedCard.cardNumber,
        updateTime: new Date()
      }
    });
  } else {
    console.log('创建新的预约记录');
    await mockDB.collection('bookings').add({
      data: {
        courseId,
        userId,
        courseName: '测试课程',
        cardNumber: selectedCard.cardNumber,
        status: 'upcoming',
        createTime: new Date(),
        updateTime: new Date()
      }
    });
  }
  
  console.log('结果: 预约成功');
  return { success: true, message: '预约成功' };
}

// 模拟取消预约逻辑
async function testCancelBooking(courseId, userId) {
  console.log(`\n=== 测试取消预约 ${courseId} (用户: ${userId}) ===`);
  
  // 查找用户的预约记录
  const bookingRes = await mockDB.collection('bookings')
    .where({ courseId, userId, status: 'upcoming' }).get();
  
  if (!bookingRes.data.length) {
    console.log('结果: 未找到可取消的预约');
    return { success: false, message: '未找到可取消的预约' };
  }
  
  const booking = bookingRes.data[0];
  console.log('找到预约记录:', booking);
  
  // 恢复会员卡次数
  const selectedCard = mockMembershipCards[0];
  console.log('恢复前会员卡次数:', selectedCard.remainingTimes);
  
  await mockDB.collection('membershipCard').doc(selectedCard._id).update({
    data: {
      remainingTimes: selectedCard.remainingTimes + 1,
      updateTime: new Date()
    }
  });
  
  console.log('恢复后会员卡次数:', selectedCard.remainingTimes);
  
  // 更新预约状态为cancelled
  await mockDB.collection('bookings').doc(booking._id).update({
    data: { status: 'cancelled', updateTime: new Date() }
  });
  
  console.log('结果: 取消成功');
  return { success: true, message: '取消成功' };
}

// 运行测试
async function runTests() {
  console.log('开始测试新的预约逻辑...\n');
  
  // 测试场景1: 首次预约
  await testBookCourse('course1', 'user1');
  console.log('当前预约记录:', mockBookings);
  
  // 测试场景2: 重复预约（应该失败）
  await testBookCourse('course1', 'user1');
  
  // 测试场景3: 取消预约
  await testCancelBooking('course1', 'user1');
  console.log('取消后预约记录:', mockBookings);
  
  // 测试场景4: 再次预约（应该更新现有记录）
  await testBookCourse('course1', 'user1');
  console.log('再次预约后记录:', mockBookings);
  
  // 测试场景5: 再次取消
  await testCancelBooking('course1', 'user1');
  console.log('最终预约记录:', mockBookings);
  
  console.log('\n最终会员卡状态:', mockMembershipCards[0]);
}

// 运行测试
runTests().catch(console.error);
