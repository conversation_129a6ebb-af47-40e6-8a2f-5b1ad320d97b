/* user-management.wxss */
.container {
  padding: 16px 16px 0 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
  height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

/* 顶部区域样式 */
.top-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  margin-bottom: 0;
}

.top-tabs-section {
  flex: 1;
  min-width: 0;
}

/* 顶部选项卡样式 */
.custom-top-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  margin: 0;
  max-width: 100%;
}

.custom-top-tabs .t-tabs__nav {
  padding: 0 8px;
}

/* 用户内容区域 */
.user-content {
  width: 100%;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-top: 16px;
}

/* 用户列表样式 */
.user-list {
  margin-bottom: 0;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.user-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/* 用户头部样式 */
.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  display: inline-block;
  white-space: nowrap;
}

.user-status.student {
  background-color: #e6f3ff;
  color: #0052d9;
}

.user-status.coach {
  background-color: #e8f5e8;
  color: #52c41a;
}

.user-status.admin {
  background-color: #fff2e8;
  color: #fa8c16;
}

.user-status.other {
  background-color: #f0f0f0;
  color: #999;
}

/* 角色标签样式 */
.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.role-tag {
  margin-right: 8px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid transparent;
}

.role-tag:last-child {
  margin-right: 0;
}

.role-tag:active {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 用户信息列表样式 */
.user-info-list {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 15px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

.info-label {
  color: #888;
  min-width: 90px;
  flex-shrink: 0;
  font-size: 15px;
}

.info-item text:last-child {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copyable-text {
  color: #0052d9;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.copyable-text:active {
  color: #003ba3;
}

/* 用户底部样式 */
.user-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  overflow-x: auto;
  padding-bottom: 2px;
  min-width: 0;
  width: 100%;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 60px;
  max-width: 80px;
  margin: 2px;
}

/* 加载和结束指示器 */
.loading-indicator,
.end-indicator {
  text-align: center;
  padding: 10px;
  color: #999;
  font-size: 15px;
}

/* 固定卡片宽度，防止内容拉伸 */
.user-card {
  width: 90vw;
  max-width: 700rpx;
  min-width: 320rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

/* 改角色弹窗样式 */
.edit-role-popup {
  background: #ffffff;
  border-radius: 16px;
  width: 80vw;
  max-width: 400px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.close-icon {
  color: #999;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-icon:active {
  color: #666;
}

.popup-content {
  padding: 20px 24px;
}

.user-info-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-id {
  display: block;
  font-size: 13px;
  color: #999;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.role-section {
  margin: 20px 0;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.section-tip {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  line-height: 1.4;
}

.role-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-checkbox-item {
  padding: 8px 0;
}

/* 禁用状态的复选框样式 */
.role-checkbox-item .t-checkbox--disabled {
  opacity: 0.6;
}

.role-checkbox-item .t-checkbox--disabled .t-checkbox__label {
  color: #999;
}

/* 讲师信息弹窗样式 */
.coach-info-popup {
  width: 90vw;
  max-width: 600px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.coach-info-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.coach-info-popup .popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.coach-info-popup .close-icon {
  color: #999;
  cursor: pointer;
}

.coach-info-popup .popup-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.coach-info-popup .form-section {
  margin-top: 20px;
}

.coach-info-popup .form-item {
  margin-bottom: 20px;
}

.coach-info-popup .form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.coach-info-popup .popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 改角色弹窗底部按钮样式 */
.edit-role-popup .popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 搜索栏样式 */
.search-section {
  margin-top: 12px;
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid #e5e5e5;
  transition: border-color 0.2s ease;
}

.search-input-container:focus-within {
  border-color: #0052d9;
}

.search-icon {
  color: #999;
  margin-right: 8px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
  background: transparent;
  min-width: 0;
}

.search-input::placeholder {
  color: #999;
}

.clear-icon {
  color: #999;
  margin-left: 8px;
  flex-shrink: 0;
  cursor: pointer;
}

.clear-icon:active {
  color: #666;
}