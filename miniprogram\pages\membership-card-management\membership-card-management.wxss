/**
 * membership-card-management.wxss - 考勤卡管理页面样式文件
 *
 * 设计：照搬course-management页面的样式设计
 * 保持界面一致性和用户体验的统一
 */

/*
 * 页面根元素样式
 * 照搬course-management的页面基础设置
 */
page {
  height: 100%;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度
 */
.page {
  height: 100%;
}

/**
 * .container: 页面根容器样式
 * 照搬course-management的容器设计
 */
.container {
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止根容器滚动 */
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  box-sizing: border-box;
}

/**
 * .top-section: 顶部区域容器 - 美化版
 * 完全照搬course-management的顶部区域设计
 */
.top-section {
  /*
   * 固定顶部区域样式
   * 让顶部选项卡固定在顶部，不随内容滚动
   */
  flex-shrink: 0; /* 防止被压缩 */
  width: 100%;
  margin-bottom: 8px; /* 与下方内容的间距 */

  /*
   * 背景设置 - 移除背景，让选项卡容器的背景生效
   */
  background: transparent;

  /*
   * 移除边框 - 让选项卡容器处理边框
   */
  border: none;

  /*
   * 移除内边距 - 让选项卡容器处理内边距
   */
  padding: 0;

  /*
   * 移除阴影 - 让选项卡容器处理阴影
   */
  box-shadow: none;

  /*
   * 移除圆角 - 让选项卡容器处理圆角
   */
  border-radius: 0;

  /*
   * 相对定位
   */
  position: relative;
}

/**
 * .top-tabs-section: 选项卡区域容器 - 美化版线条选项卡
 * 照搬course-management的渐变背景和装饰效果
 */
.top-tabs-section {
  /*
   * 宽度和布局
   */

  position: relative;

  /*
   * 背景设计 - 渐变背景增加层次感
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 精致的边框效果
   */
  border: 1px solid #f0f0f0;
  /*border-radius: 8px 8px 0 0;  上圆角，下直角 */
  border-radius: 8px;


  /*
   * 阴影效果 - 轻微的阴影增加浮起感
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 0px 16px 0 16px;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 底部装饰线 - 与内容区域的分隔
   */
  border-bottom: 1px solid #e7e7e7;

  /*
   * 层级控制
   */
  z-index: 10;
}

/*
 * 悬停效果 - 增加交互反馈
 * 照搬course-management的悬停设计
 */
.top-tabs-section:hover {
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 82, 217, 0.08);
}

/**
 * .custom-top-tabs: 自定义线条选项卡样式 - 美化版
 * 照搬course-management的完整t-tab样式设计
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 字体设置
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * TDesign线条选项卡导航区域样式 - 美化版
 * 照搬course-management的导航区域设计
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;

  /*
   * 边框控制
   */
  border: none;
  background: transparent;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

/**
 * 线条选项卡项目样式 - 美化版
 * 照搬course-management的选项卡项目设计
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 字体设置 - 优化可读性
   */
  font-size: 16px !important;
  font-weight: 500;

  /*
   * 内边距 - 增加舒适的点击区域
   */
  padding: 14px 20px !important;

  /*
   * 高度控制
   */
  height: auto;
  line-height: 1.4;
  min-height: 44px;

  /*
   * 布局控制
   */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /*
   * 移除圆角和背景
   */
  border-radius: 0;
  background: transparent !important;

  /*
   * 底部边框 - 激活状态指示器
   */
  border-bottom: 3px solid transparent; /* 增加指示器厚度 */

  /*
   * 过渡动画 - 更流畅的动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 文字颜色
   */
  color: #666666 !important;

  /*
   * 相对定位用于伪元素
   */
  position: relative;
}

/*
 * 选项卡项目的装饰效果 - 顶部渐变线条
 * 照搬course-management的渐变线条设计
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡 - 美化版
 * 照搬course-management的激活状态设计
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部蓝色指示线 - 更粗更明显
   */
  border-bottom-color: #0052d9 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 文字阴影效果 - 增加层次感
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/*
 * 激活状态的顶部装饰线
 * 照搬course-management的激活装饰线设计
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果 - 美化版
 * 照搬course-management的悬停效果设计
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 悬停时的底部边框效果
   */
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;

  /*
   * 轻微的文字阴影
   */
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/*
 * 悬停时的顶部装饰线
 * 照搬course-management的悬停装饰线设计
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器
 * 照搬course-management的底部指示线设计
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

/**
 * 筛选区域样式
 * 照搬course-management的筛选区域设计
 */
.filter-section {
  width: 100%;
  padding-right: 16px;
  padding-left: 16px;
  box-sizing: border-box;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保筛选区域在内容溢出时不会被压缩
   */
  flex-shrink: 0;

  /*
   * background-color: 白色背景
   * 与页面背景形成对比
   */
  background-color: #fff;

  /*
   * border-radius: 圆角设计
   * 与顶部区域保持一致
   */
  border-radius: 8px;

  /*
   * margin-bottom: 底部外边距
   * 与内容区域保持适当间距
   */
  margin-bottom: 16px;

  /*
   * padding: 内边距
   * 为筛选内容提供舒适的内边距
   */
  padding: 16px;

  /*
   * box-shadow: 阴影效果
   * 与顶部区域保持一致的阴影
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/**
 * 状态选项卡样式
 * 照搬course-management的booking-tabs设计
 */
.booking-tabs {
  /*
   * display: flex - 弹性布局
   * 让选项卡水平排列
   */
  display: flex;

  /*
   * background-color: 浅灰色背景
   * #f8f9fa: 非常浅的灰色，提供微妙的背景对比
   */
  background-color: #f8f9fa;

  /*
   * border-radius: 圆角设计
   * 6px: 适中的圆角，与整体设计保持一致
   */
  border-radius: 6px;

  /*
   * padding: 内边距
   * 4px: 为选项卡提供紧凑的内边距
   */
  padding: 4px;

  /*
   * margin-bottom: 底部外边距
   * 16px: 与搜索区域保持适当间距
   */
  margin-bottom: 16px;
}

/**
 * 单个选项卡样式
 * 照搬course-management的booking-tab设计
 */
.booking-tab {
  /*
   * flex: 1 - 等分剩余空间
   * 让所有选项卡平均分配宽度
   */
  flex: 1;

  /*
   * text-align: center - 文字居中
   * 让选项卡文字在容器中居中显示
   */
  text-align: center;

  /*
   * padding: 内边距
   * 8px 16px: 垂直8px，水平16px，提供舒适的点击区域
   */
  padding: 8px 16px;

  /*
   * border-radius: 圆角
   * 4px: 比容器稍小的圆角，形成内嵌效果
   */
  border-radius: 4px;

  /*
   * font-size: 字体大小
   * 14px: 适中的字体大小，保证可读性
   */
  font-size: 14px;

  /*
   * font-weight: 字体粗细
   * 500: 中等粗细，介于normal和bold之间
   */
  font-weight: 500;

  /*
   * color: 文字颜色
   * #666: 中等灰色，未选中状态的文字颜色
   */
  color: #666;

  /*
   * cursor: 鼠标样式
   * pointer: 指针样式，表示可点击
   */
  cursor: pointer;

  /*
   * transition: 过渡动画
   * all 0.2s ease: 所有属性在0.2秒内平滑过渡
   */
  transition: all 0.2s ease;
}

/**
 * 选中状态的选项卡样式
 * 照搬course-management的active状态设计
 */
.booking-tab.active {
  /*
   * background-color: 白色背景
   * 选中状态使用白色背景，形成突出效果
   */
  background-color: #fff;

  /*
   * color: 主题色文字
   * #1890ff: 蓝色主题色，表示选中状态
   */
  color: #1890ff;

  /*
   * box-shadow: 阴影效果
   * 轻微阴影增加立体感
   */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/**
 * 搜索和操作区域样式
 * 照搬course-management的search-actions-section设计
 */
.search-actions-section {
  /*
   * transition: 过渡动画
   * 为展开/收起状态提供平滑过渡
   */
  transition: all 0.3s ease;
}

/**
 * 收起状态布局
 * 照搬course-management的collapsed-layout设计
 */
.collapsed-layout {
  /*
   * display: flex - 弹性布局
   * 让搜索图标和按钮水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * 确保图标和按钮在垂直方向上居中对齐
   */
  align-items: center;

  /*
   * gap: 间距
   * 12px: 搜索图标和按钮之间的间距
   */
  gap: 12px;
}

/**
 * 搜索图标容器样式
 * 照搬course-management的search-icon-only设计
 */
.search-icon-only {
  /*
   * display: flex - 弹性布局
   * 让图标在容器中居中
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * justify-content: center - 水平居中
   */
  align-items: center;
  justify-content: center;

  /*
   * width, height: 尺寸设置
   * 40px: 提供足够大的点击区域
   */
  width: 40px;
  height: 40px;

  /*
   * border-radius: 圆角
   * 50%: 圆形按钮
   */
  border-radius: 50%;

  /*
   * background-color: 背景色
   * #f8f9fa: 浅灰色背景
   */
  background-color: #f8f9fa;

  /*
   * cursor: 鼠标样式
   * pointer: 指针样式，表示可点击
   */
  cursor: pointer;

  /*
   * transition: 过渡动画
   * 为悬停效果提供平滑过渡
   */
  transition: all 0.2s ease;
}

/**
 * 搜索图标悬停效果
 * 照搬course-management的悬停设计
 */
.search-icon-only:hover {
  /*
   * background-color: 悬停背景色
   * #e9ecef: 稍深的灰色，提供视觉反馈
   */
  background-color: #e9ecef;
}

/**
 * 操作按钮容器样式
 * 照搬course-management的actions-container设计
 */
.actions-container {
  /*
   * display: flex - 弹性布局
   * 让按钮水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * 确保按钮在垂直方向上居中对齐
   */
  align-items: center;

  /*
   * gap: 间距
   * 12px: 按钮之间的间距
   */
  gap: 12px;


}


.actions-container-button{
  width: fit-content;
}





/**
 * 展开状态布局
 * 照搬course-management的expanded-layout设计
 */
.expanded-layout {
  /*
   * width: 100% - 占满容器宽度
   * 展开状态下搜索框占据全部宽度
   */
  width: 100%;
}

/**
 * 搜索输入容器样式
 * 照搬course-management的search-input-container设计
 */
.search-input-container {
  /*
   * display: flex - 弹性布局
   * 让搜索相关元素水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * 确保图标和输入框在垂直方向上居中对齐
   */
  align-items: center;

  /*
   * background-color: 白色背景
   * 与输入框形成统一的背景
   */
  background-color: #fff;

  /*
   * border: 边框
   * 1px solid #e0e0e0: 浅灰色边框
   */
  border: 1px solid #e0e0e0;

  /*
   * border-radius: 圆角
   * 20px: 较大的圆角，形成胶囊形状
   */
  border-radius: 20px;

  /*
   * padding: 内边距
   * 8px 16px: 垂直8px，水平16px
   */
  padding: 8px 16px;

  /*
   * gap: 间距
   * 8px: 图标和输入框之间的间距
   */
  gap: 8px;
}

/**
 * 搜索图标样式
 * 照搬course-management的search-icon设计
 */
.search-icon {
  /*
   * color: 图标颜色
   * #999: 中等灰色，不会过于突出
   */
  color: #999;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保图标尺寸不会因为输入框内容而改变
   */
  flex-shrink: 0;
}

/**
 * 搜索输入框样式
 * 照搬course-management的search-input设计
 */
.search-input {
  /*
   * flex: 1 - 占据剩余空间
   * 让输入框占据除图标外的所有空间
   */
  flex: 1;

  /*
   * border: none - 移除边框
   * 使用容器的边框，避免双重边框
   */
  border: none;

  /*
   * outline: none - 移除焦点轮廓
   * 使用自定义的焦点样式
   */
  outline: none;

  /*
   * background: transparent - 透明背景
   * 使用容器的背景色
   */
  background: transparent;

  /*
   * font-size: 字体大小
   * 14px: 适中的字体大小，保证可读性
   */
  font-size: 14px;

  /*
   * color: 文字颜色
   * #333: 深灰色，保证良好的对比度
   */
  color: #333;
}

/**
 * 搜索输入框占位符样式
 * 照搬course-management的placeholder设计
 */
.search-input::placeholder {
  /*
   * color: 占位符颜色
   * #999: 中等灰色，与输入文字形成对比
   */
  color: #999;
}

/**
 * 清空图标样式
 * 照搬course-management的clear-icon设计
 */
.clear-icon {
  /*
   * color: 图标颜色
   * #999: 中等灰色
   */
  color: #999;

  /*
   * cursor: 鼠标样式
   * pointer: 指针样式，表示可点击
   */
  cursor: pointer;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保图标尺寸固定
   */
  flex-shrink: 0;

  /*
   * transition: 过渡动画
   * 为悬停效果提供平滑过渡
   */
  transition: color 0.2s ease;
}

/**
 * 清空图标悬停效果
 * 照搬course-management的悬停设计
 */
.clear-icon:hover {
  /*
   * color: 悬停颜色
   * #666: 稍深的灰色，提供视觉反馈
   */
  color: #666;
}

/**
 * 收起图标样式
 * 照搬course-management的collapse-icon设计
 */
.collapse-icon {
  /*
   * color: 图标颜色
   * #999: 中等灰色
   */
  color: #999;

  /*
   * cursor: 鼠标样式
   * pointer: 指针样式，表示可点击
   */
  cursor: pointer;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保图标尺寸固定
   */
  flex-shrink: 0;

  /*
   * transition: 过渡动画
   * 为悬停效果提供平滑过渡
   */
  transition: color 0.2s ease;
}

/**
 * 收起图标悬停效果
 * 照搬course-management的悬停设计
 */
.collapse-icon:hover {
  /*
   * color: 悬停颜色
   * #666: 稍深的灰色，提供视觉反馈
   */
  color: #666;
}

/**
 * 内容区域样式
 * 照搬course-management的course-content设计
 */
.course-content {
  /*
   * flex: 1 - 占据剩余空间
   * 让内容区域占据除顶部和筛选区域外的所有空间
   */
  flex: 1;

  /*
   * display: flex - 弹性布局
   * 为内部的列表容器提供弹性布局基础
   */
  display: flex;

  /*
   * flex-direction: column - 垂直方向
   * 让内容垂直排列
   */
  flex-direction: column;

  /*
   * min-height: 0 - 最小高度
   * 允许内容区域收缩，配合flex布局
   */
  min-height: 0;
}

/**
 * 列表区域样式
 * 照搬course-management的course-list设计
 */
.course-list {
  /*
   * flex: 1 - 占据剩余空间
   * 让列表区域占据内容区域的所有空间
   */
  flex: 1;

  /*
   * display: flex - 弹性布局
   * 为内部的滚动容器提供弹性布局基础
   */
  display: flex;

  /*
   * flex-direction: column - 垂直方向
   * 让列表内容垂直排列
   */
  flex-direction: column;

  /*
   * min-height: 0 - 最小高度
   * 允许列表区域收缩，配合flex布局
   */
  min-height: 0;
}

/**
 * 卡片样式
 * 照搬course-management的course-card设计
 */
.course-card {
  /*
   * background-color: 白色背景
   * 与页面背景形成对比
   */
  background-color: #fff;

  /*
   * border-radius: 圆角设计
   * 8px: 与其他元素保持一致的圆角
   */
  border-radius: 8px;

  /*
   * margin-bottom: 底部外边距
   * 12px: 卡片之间的间距
   */
  margin-bottom: 12px;

  /*
   * padding: 内边距
   * 16px: 为卡片内容提供舒适的内边距
   */
  padding: 16px;

  /*
   * box-shadow: 阴影效果
   * 轻微的阴影增加层次感
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /*
   * transition: 过渡动画
   * 为悬停效果提供平滑过渡
   */
  transition: all 0.2s ease;
}

/**
 * 卡片悬停效果
 * 照搬course-management的悬停设计
 */
.course-card:hover {
  /*
   * box-shadow: 悬停阴影
   * 更明显的阴影，提供视觉反馈
   */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

  /*
   * transform: 变换效果
   * translateY(-2px): 轻微上移，增加悬浮感
   */
  transform: translateY(-2px);
}

/**
 * 卡片头部样式
 * 照搬course-management的course-header设计
 */
.course-header {
  /*
   * display: flex - 弹性布局
   * 让标题和状态标签水平排列
   */
  display: flex;

  /*
   * justify-content: space-between - 两端对齐
   * 标题在左，状态标签在右
   */
  justify-content: space-between;

  /*
   * align-items: flex-start - 顶部对齐
   * 确保标题和标签在顶部对齐
   */
  align-items: flex-start;

  /*
   * margin-bottom: 底部外边距
   * 12px: 与信息列表保持适当间距
   */
  margin-bottom: 12px;
}

/**
 * 标题行样式
 * 照搬course-management的course-title-row设计
 */
.course-title-row {
  /*
   * display: flex - 弹性布局
   * 让标题和其他元素水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * 确保标题和其他元素在垂直方向上居中对齐
   */
  align-items: center;

  /*
   * flex: 1 - 占据剩余空间
   * 让标题行占据除状态标签外的所有空间
   */
  flex: 1;

  /*
   * min-width: 0 - 最小宽度
   * 允许标题行收缩，避免溢出
   */
  min-width: 0;
}

/**
 * 标题样式
 * 照搬course-management的course-title设计
 */
.course-title {
  /*
   * font-size: 字体大小
   * 16px: 较大的字体，突出标题重要性
   */
  font-size: 16px;

  /*
   * font-weight: 字体粗细
   * 600: 半粗体，让标题更突出
   */
  font-weight: 600;

  /*
   * color: 文字颜色
   * #333: 深灰色，保证良好的对比度
   */
  color: #333;

  /*
   * margin-right: 右边距
   * 12px: 与其他元素保持适当间距
   */
  margin-right: 12px;

  /*
   * overflow: hidden - 隐藏溢出
   * text-overflow: ellipsis - 省略号
   * white-space: nowrap - 不换行
   * 长标题显示省略号
   */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**
 * 状态标签样式
 * 照搬course-management的course-status设计
 */
.course-status {
  /*
   * display: inline-block - 行内块元素
   * 让标签可以设置宽高，同时保持行内特性
   */
  display: inline-block;

  /*
   * padding: 内边距
   * 4px 8px: 垂直4px，水平8px，提供紧凑的内边距
   */
  padding: 4px 8px;

  /*
   * border-radius: 圆角
   * 4px: 小圆角，与整体设计保持一致
   */
  border-radius: 4px;

  /*
   * font-size: 字体大小
   * 12px: 较小的字体，不会过于突出
   */
  font-size: 12px;

  /*
   * font-weight: 字体粗细
   * 500: 中等粗细
   */
  font-weight: 500;

  /*
   * white-space: nowrap - 不换行
   * 确保状态文字在一行内显示
   */
  white-space: nowrap;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保状态标签不会因为标题过长而被压缩
   */
  flex-shrink: 0;
}

/**
 * 状态标签 - 在线状态（已绑定）
 * 照搬course-management的online状态设计
 */
.course-status.online {
  /*
   * background-color: #e6f7ff - 浅蓝色背景
   * 蓝色系表示正常、活跃状态
   */
  background-color: #e6f7ff;
  /*
   * color: #1890ff - 蓝色文字
   * 与背景形成良好对比，保证可读性
   */
  color: #1890ff;
}

/**
 * 状态标签 - 离线状态（即将到期）
 * 照搬course-management的offline状态设计
 */
.course-status.offline {
  /*
   * background-color: #fff7e6 - 浅橙色背景
   * 橙色系表示警告、注意状态
   */
  background-color: #fff7e6;
  /*
   * color: #fa8c16 - 橙色文字
   * 与背景形成良好对比，传达警告信息
   */
  color: #fa8c16;
}

/**
 * 状态标签 - 结束状态（已过期）
 * 照搬course-management的ended状态设计
 */
.course-status.ended {
  /*
   * background-color: #fff1f0 - 浅红色背景
   * 红色系表示错误、结束状态
   */
  background-color: #fff1f0;
  /*
   * color: #f5222d - 红色文字
   * 与背景形成良好对比，传达重要信息
   */
  color: #f5222d;
}

/**
 * 状态标签 - 无状态（未绑定）
 * 照搬course-management的no-status状态设计
 */
.course-status.no-status {
  /*
   * background-color: #f0f0f0 - 浅灰色背景
   * 中性色，不传达特定含义
   */
  background-color: #f0f0f0;
  /*
   * color: #999 - 中灰色文字
   * 比ended状态稍浅，表示这是中性状态
   */
  color: #999;
}

/**
 * 模板状态标签
 * 照搬course-management的template-status设计
 */
.course-status.template-status {
  /*
   * background-color: #e6f3ff - 浅蓝色背景
   * 与活动的"已预约"状态使用相同颜色
   * 蓝色传达"信息性"、"模板"的含义
   */
  background-color: #e6f3ff;
  /*
   * color: #1890ff - 蓝色文字
   * 与背景形成良好对比，保证可读性
   * 与TDesign的primary主题色保持一致
   */
  color: #1890ff;
}

/**
 * 信息列表样式
 * 照搬course-management的course-info-list设计
 */
.course-info-list {
  /*
   * margin-bottom: 底部外边距
   * 16px: 与操作按钮区域保持适当间距
   */
  margin-bottom: 16px;
}

/**
 * 信息项样式
 * 照搬course-management的info-item设计
 */
.info-item {
  /*
   * display: flex - 弹性布局
   * 让图标和文字水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中
   * 确保图标和文字在垂直方向上居中对齐
   */
  align-items: center;

  /*
   * margin-bottom: 底部外边距
   * 8px: 信息项之间的间距
   */
  margin-bottom: 8px;

  /*
   * font-size: 字体大小
   * 14px: 适中的字体大小，保证可读性
   */
  font-size: 14px;

  /*
   * color: 文字颜色
   * #666: 中等灰色，不会过于突出
   */
  color: #666;
}

/**
 * 信息项中的图标样式
 * 照搬course-management的图标设计
 */
.info-item .t-icon {
  /*
   * margin-right: 右边距
   * 8px: 图标和文字之间的间距
   */
  margin-right: 8px;

  /*
   * color: 图标颜色
   * #999: 比文字稍浅的灰色
   */
  color: #999;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保图标尺寸不会因为文字过长而改变
   */
  flex-shrink: 0;
}

/**
 * 操作按钮区域样式
 * 照搬course-management的course-footer设计
 */
.course-footer {
  /*
   * border-top: 顶部边框
   * 1px solid #f0f0f0: 浅灰色边框，与内容区域分隔
   */
  border-top: 1px solid #f0f0f0;

  /*
   * padding-top: 顶部内边距
   * 12px: 与边框保持适当间距
   */
  padding-top: 12px;
}

/**
 * 操作按钮容器样式
 * 照搬course-management的action-buttons设计
 */
.action-buttons {
  /*
   * display: flex - 弹性布局
   * 让按钮水平排列
   */
  display: flex;

  /*
   * gap: 间距
   * 8px: 按钮之间的间距
   */
  gap: 8px;

  /*
   * flex-wrap: wrap - 允许换行
   * 当按钮过多时可以换行显示
   */
  flex-wrap: wrap;
}

/**
 * 操作按钮样式
 * 照搬course-management的按钮设计
 */
.action-buttons .t-button {
  /*
   * flex: 1 - 等分剩余空间
   * 让按钮平均分配宽度
   */
  flex: 1;

  /*
   * min-width: 最小宽度
   * 80px: 确保按钮有足够的点击区域
   */
  min-width: 80px;
}

/* 保留原有的其他样式 */

/* 模板列表样式 */
.template-list {
  margin: 16px;
}

.template-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-bottom: 20rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.template-info-list {
  padding-left: 0;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 6rpx;
  gap: 8rpx;
}

.info-item text {
  color: #666;
}

.template-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.action-buttons {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.action-buttons .t-button {
  flex: 1;
  min-width: 0;
}

/* 原有的顶部栏样式（保留兼容性） */
.top-bar {
  width: 100%;
  padding: 16px 16px 0 16px;
  background: #fff;
}

.create-btn-bar {
  padding: 16px 16px 0 16px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
}
.create-btn {
  margin-left: 0;
}
.card-list {
  margin: 16px;
}
.membership-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-bottom: 20rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

/* 统一 t-tag 样式 */
.card-header t-tag {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  flex-shrink: 0;
  white-space: nowrap;
}
.card-number {
  font-size: 16px;
  font-weight: 600;
  margin-left: 12rpx;
  flex: 1;
}
.card-body {
  padding-left: 40rpx;
}
.card-row {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 6rpx;
}
.card-row text:first-child {
  color: #888;
  min-width: 80rpx;
}
.card-row text:last-child {
  color: #222;
  font-weight: 500;
}
.form-row {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.static-date {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #f8f9fa;
  min-height: 48rpx;
}

.static-date-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.static-date-hint {
  font-size: 24rpx;
  color: #999;
}
.form-row:last-child {
  margin-bottom: 0;
}
.card-actions {
  display: flex;
  gap: 8rpx;
  margin-top: 16rpx;
  align-items: center;
}

.card-actions .t-button {
  flex: 1;
  min-width: 0;
}

.button-spacer {
  flex: 1;
  min-width: 0;
  height: 56rpx;
}

/* 自定义按钮样式，改善 light 主题按钮的外观 */
.card-actions .t-button--theme-light {
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  color: #666;
}

.card-actions .t-button--theme-light:active {
  background: #e8e8e8;
  border-color: #d0d0d0;
}

.card-actions .t-button--theme-light:hover {
  background: #f0f0f0;
  border-color: #d8d8d8;
} 

/* 新建会员卡弹窗样式 */
.dialog-content {
  padding: 32rpx;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 32rpx;
} 

/* 自定义弹窗样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-dialog {
  width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dialog-content {
  padding: 32rpx;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 绑定弹窗提示样式 */
.form-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 确认弹窗样式 */
.confirm-info {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
  margin-right: 16rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.openid-text {
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  color: #0052d9;
  word-break: break-all;
}

.confirm-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #fff7e6;
  border-radius: 8rpx;
  border-left: 4rpx solid #fa8c16;
}

.revoke-tip {
  background: #fff2f0;
  border-left-color: #e34d59;
}

/* 应用模板弹窗样式 */
.template-info {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.template-info .info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.template-info .info-row:last-child {
  margin-bottom: 0;
}

.template-info .info-label {
  color: #666;
  font-size: 28rpx;
  min-width: 140rpx;
}

.template-info .info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 开关容器样式 - 与输入框保持一致 */
.switch-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0;
  margin-bottom: 24rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  min-width: 140rpx;
  margin-right: 16rpx;
}

/* 确保开关容器与其他表单项对齐 */
.form-row .switch-container {
  padding: 0;
  margin-bottom: 24rpx;
}

/* 统一模板弹窗中的标签样式 */
.custom-dialog .t-input__label,
.custom-dialog .t-textarea__label,
.custom-dialog .switch-label {
  font-size: 28rpx !important;
  color: #333 !important;
  font-weight: 500 !important;
  min-width: 140rpx !important;
  display: inline-block !important;
}

/* 统一输入框和文本域的样式 */
.custom-dialog .t-input,
.custom-dialog .t-textarea {
  font-size: 28rpx;
}

.custom-dialog .t-input__control,
.custom-dialog .t-textarea__control {
  font-size: 28rpx;
}

/* 统一占位符文字样式 */
.custom-dialog .t-input__placeholder,
.custom-dialog .t-textarea__placeholder {
  font-size: 28rpx;
  color: #999;
}

/* 统一按钮文字样式 */
.custom-dialog .t-button {
  font-size: 28rpx;
  font-weight: 500;
} 