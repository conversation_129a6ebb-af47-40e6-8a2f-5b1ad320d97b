<!--my-bookings.wxml-->
<view class="container">


  <!-- 预约状态筛选栏 -->
  <view class="filter-section">
    <view class="booking-tabs">
      <view 
        class="booking-tab {{activeTab === 'all' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="all"
      >
        全部
      </view>
      <view 
        class="booking-tab {{activeTab === 'upcoming' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="upcoming"
      >
        已预约
      </view>
      <view 
        class="booking-tab {{activeTab === 'cancelled' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="cancelled"
      >
        已取消
      </view>
      <view 
        class="booking-tab {{activeTab === 'completed' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="completed"
      >
        已完成
      </view>
    </view>
  </view>

  <!-- 全部活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'all'}}">
    <t-empty wx:if="{{filteredAllBookings.length === 0}}" description="暂无活动记录" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onAllScrollToLower"
      bindscrolltoupper="onAllScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggered}}"
      bindrefresherrefresh="onRefresherRefresh"
    >
      <view wx:if="{{isLoadingTop}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistory}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleAllBookings}}" wx:key="id">
        <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
        <view wx:if="{{index === 0 || visibleAllBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
          <view class="booking-actions">
            <t-button 
              wx:if="{{item.canCancel}}"
              size="small" 
              theme="default"
              variant="outline"
              bind:tap="cancelBooking"
              data-booking="{{item}}"
            >
              取消预约
            </t-button>
          </view>
        </view>
      </block>
      <view wx:if="{{isLoadingBottom}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFuture}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- 未上活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'upcoming'}}">
    <t-empty wx:if="{{filteredUpcomingBookings.length === 0}}" description="暂无已预约活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;">
      <view 
        class="booking-card" 
        wx:for="{{filteredUpcomingBookings}}" 
        wx:key="id"
        bind:tap="viewCourseDetail"
        data-course="{{item}}"
      >
        <view class="booking-status {{item.status}}">{{item.statusText}}</view>
        <view class="course-title">{{item.courseName}}</view>
        <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
          <view></view>
        </view>
        <view class="course-info-list">
          <view class="info-item">
            <t-icon name="time" size="16" />
            <text>{{item.date}} {{item.time}}</text>
          </view>
          <view class="info-item">
            <t-icon name="user" size="16" />
            <text>{{item.coach}}</text>
          </view>
          <view class="info-item">
            <t-icon name="location" size="16" />
            <text>{{item.venue}}</text>
          </view>
        </view>
        <view class="booking-actions">
          <t-button 
            wx:if="{{item.canCancel}}"
            size="small" 
            theme="default"
            variant="outline"
            bind:tap="cancelBooking"
            data-booking="{{item}}"
          >
            取消预约
          </t-button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 已取消活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'cancelled'}}">
    <t-empty wx:if="{{filteredCancelledBookings.length === 0}}" description="暂无已取消活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onCancelledScrollToLower"
      bindscrolltoupper="onCancelledScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggeredCancelled}}"
      bindrefresherrefresh="onRefresherRefreshCancelled"
    >
      <view wx:if="{{isLoadingTopCancelled}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistoryCancelled}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleCancelledBookings}}" wx:key="id">
        <view wx:if="{{index === 0 || visibleCancelledBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexesCancelled.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
          <!-- booking-actions 不再显示按钮 -->
        </view>
      </block>
      <view wx:if="{{isLoadingBottomCancelled}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFutureCancelled}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- 已完成活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'completed'}}">
    <t-empty wx:if="{{filteredCompletedBookings.length === 0}}" description="暂无已完成活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onCompletedScrollToLower"
      bindscrolltoupper="onCompletedScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggeredCompleted}}"
      bindrefresherrefresh="onRefresherRefreshCompleted"
    >
      <view wx:if="{{isLoadingTopCompleted}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistoryCompleted}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleCompletedBookings}}" wx:key="id">
        <view wx:if="{{index === 0 || visibleCompletedBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexesCompleted.indexOf(index) !== -1 ? ' slide-in' : ''}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
        </view>
      </block>
      <view wx:if="{{isLoadingBottomCompleted}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFutureCompleted}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>
  <t-toast id="t-toast" />
</view>